# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is **PMS (Price Management System) API v2** - an enterprise-level price inquiry management system for government procurement. The system supports the complete workflow of inquiry, quotation, review, and reference price calculation. Built with Go 1.24+ using a clean, pragmatic architecture focused on solving real business problems.

## Commands

### Build and Run
```bash
make run                          # Run the API server directly  
make dev                          # Run using main.go wrapper
make build                        # Build the binary to bin/pms-api
make clean                        # Clean build artifacts
go run cmd/api/main.go           # Run API server directly with go
```

### Code Generation
```bash
sqlc generate                     # Generate database access code from SQL files
go generate ./...                 # Generate all go:generate directives
```

### Testing
```bash
go test ./...                     # Run all tests
go test -v -race ./...           # Run tests with verbose output and race detection
go test -coverprofile=coverage.out ./...  # Run tests with coverage
go test ./internal/package_name   # Run tests for specific package
go test -run TestFunctionName     # Run specific test function
```

### Code Quality and Linting
```bash
# Makefile shortcuts
make quality                      # Full quality check (format, lint, test, security)
make test                         # Run tests with race detection
make fmt                          # Format code
make vet                          # Static analysis
make tools                        # Install development tools
make deps                         # Install/update dependencies

# Direct commands
./scripts/quality.sh              # Full quality check script
go fmt ./...                      # Format code
go vet ./...                      # Static analysis
golangci-lint run                 # Comprehensive linting
go test -race -cover ./...        # Tests with race detection and coverage
gosec ./...                       # Security analysis
```

### Database Operations
```bash
# Database migrations (when migrate tool is available)
migrate -path migrations -database "$DATABASE_URL" up
migrate -path migrations -database "$DATABASE_URL" down

# Generate database code from SQL queries
sqlc generate                     # Generates Go code in sqlc/ from queries/
```

## Architecture

### Core Business Flow
The system revolves around the lifecycle of inquiry projects:
1. **Vendor Registration & Review**: Vendors register and require CISA approval
2. **Project Management**: SPO creates inquiry projects with time windows
3. **Product Import**: Import product lists from external systems
4. **Quotation Process**: Vendors submit quotes within time windows, CISA reviews
5. **Reference Price Calculation**: System calculates reference prices using multiple principles
6. **Reminder Notifications**: Automated email reminders for non-responding vendors

### System Roles
- **SPO (Software Procurement Office)**: System admin with highest privileges
- **CISA (Computer & Information Service Association)**: Middle reviewer role
- **Company (Vendors)**: General users who submit quotes

### Technology Stack
- **Go 1.24+**: Leverages standard library enhancements, especially net/http routing
- **PostgreSQL + pgx**: High-performance database driver with full PostgreSQL features
- **sqlc**: Compile-time SQL validation and type-safe database operations
- **slog**: Official structured logging for easy Loki integration
- **JWT (Cookie-based)**: Secure authentication preventing XSS attacks

### Package Structure Philosophy

Based on Go best practices and avoiding common anti-patterns:

#### Core Principles
- **Functional Cohesion**: Each package handles one clear business domain
- **Minimal Dependencies**: Packages communicate through interfaces, avoiding circular deps
- **Clear Naming**: Package names directly reflect functionality
- **Flat Structure**: Avoid deep nesting for simplicity

#### Package Naming Guidelines
**AVOID generic package names:**
- ❌ `handlers`, `models`, `utils`, `common` - these provide no semantic value
- ❌ MVC-style organization (`models/user.go`, `controllers/user_controller.go`)
- ❌ Technical layer grouping instead of business domain grouping

**PREFER domain-focused packages:**
- ✅ `user`, `company`, `project`, `auth` - clear business domains
- ✅ Single responsibility per package
- ✅ Package name provides context (e.g., `user.New()` instead of `user.NewUser()`)

**Example of correct structure:**
```
// GOOD: Domain-oriented packages
user/
├── user.go        // Domain types
├── service.go     // Business logic
├── handler.go     // HTTP handlers
└── repository.go  // Data persistence

// BAD: Technical layer packages
models/
├── user.go
├── order.go
controllers/
├── user_controller.go
├── order_controller.go
```

### Expected Directory Layout
Based on the Architecture.md, the mature project structure should include:
```
cmd/api/                  # API service entry point
internal/                 # Internal implementation packages
├── auth/                # Authentication & authorization
├── user/                # User management
├── company/             # Vendor management
├── project/             # Project management
├── quote/               # Quote management
├── reference/           # Reference price calculation
└── api/                 # HTTP server and routing
sqlc/                    # Generated database code
migrations/              # Database migration files
queries/                 # SQL query files for sqlc
```

### Key Implementation Details

**Authentication**: JWT tokens stored in HTTP-only cookies for XSS protection

**Database**: Uses sqlc for type-safe, compile-time validated SQL queries

**Transaction Handling**: Implements a `WithTx` pattern for consistent transaction management

**Error Handling**: Structured error system with business error codes

**Logging**: Structured logging with slog, including request tracking and business metrics

**Reference Price Calculation**: Core algorithm with 4 calculation principles based on different data sources

**Database Schema**: Comprehensive PostgreSQL 17+ schema with:
- Custom ENUM types for roles, statuses, and business domains
- JSONB fields for flexible data (product attributes, calculation details)
- Proper indexing for performance optimization
- Detailed column comments for documentation
- Audit fields (created_at, updated_at) on all tables

### PostgreSQL 17 Best Practices

#### Performance Features
PostgreSQL 17 brings significant performance improvements:
- **Vacuum optimization**: 20x memory reduction, combined freeze/prune operations
- **WAL performance**: 2x write throughput for high-concurrency workloads
- **Streaming I/O**: Vectorized I/O for faster sequential scans and ANALYZE

#### Query Optimization
```sql
-- Always use EXPLAIN with BUFFERS for production analysis
EXPLAIN (ANALYZE, BUFFERS, TIMING) 
SELECT * FROM orders WHERE status = 'active';

-- Monitor key metrics:
-- - Shared hit/read ratio (target >95% cache hit)
-- - Actual vs estimated rows (large deviation = stale stats)
-- - Time spent per node (identify bottlenecks)
```

#### Index Strategy
Choose the right index type:
- **B-tree** (default): Equality, range queries, sorting - best for high selectivity
- **GIN**: JSONB, arrays, full-text search - best for complex data types
- **Partial indexes**: Filter data at index level for better performance

```sql
-- JSONB indexing with jsonb_path_ops (smaller, faster)
CREATE INDEX idx_metadata_path 
ON products USING GIN (metadata jsonb_path_ops);

-- Partial index for active records only
CREATE INDEX idx_active_orders 
ON orders (order_date) 
WHERE status = 'active';
```

#### pgx v5 Integration
```go
// Production-ready connection pool
config.MaxConns = 30                      // Based on CPU cores
config.MinConns = 5                       // Keep minimum connections
config.MaxConnLifetime = time.Hour        // Connection rotation
config.MaxConnIdleTime = 15 * time.Minute // Close idle connections
config.HealthCheckPeriod = time.Minute    // Regular health checks

// Use pgx v5 generic row collection
orders, err := pgx.CollectRows(rows, pgx.RowToStructByName[Order])
```

## Observability

### Structured Logging with slog
The project uses slog for structured logging with Loki integration:
```go
// Add trace context to logs
logger.InfoContext(ctx, "Request processed",
    slog.String("service", "pms-api"),
    slog.String("trace_id", traceID),
    slog.String("method", r.Method),
    slog.String("path", r.URL.Path),
    slog.Int("status", statusCode))

// IMPORTANT: Keep Loki labels low cardinality
// Good: service, environment, level
// Bad: user_id, request_id, unique values
```

### OpenTelemetry Integration
Current status (2024-2025):
- **Traces**: Stable (production-ready)
- **Metrics**: Beta (production-ready)
- **Logs**: Experimental (may have breaking changes)

### Metrics Strategy
Follow the RED method:
- **Rate**: Requests per second
- **Errors**: Failed requests
- **Duration**: Request latency

```go
// Record metrics for every request
m.requestsTotal.Add(ctx, 1, labels)
m.requestDuration.Record(ctx, duration.Seconds(), labels)
if statusCode >= 400 {
    m.requestErrors.Add(ctx, 1, labels)
}
```

### Performance Impact
When properly configured:
- CPU overhead: 3-10%
- Memory overhead: 5-15%
- Latency impact: <1ms with 1% sampling
- Use head-based sampling for performance
- Batch exports to reduce network overhead

## Development Workflow

### Before Every Commit
**ALWAYS run the quality script to ensure code passes all Go code review tools:**
```bash
./scripts/quality.sh
```

This script automatically:
- ✅ Formats code with `gofmt` and `goimports`
- ✅ Runs static analysis with `go vet`
- ✅ Performs comprehensive linting with `golangci-lint`
- ✅ Executes tests with race detection and coverage reporting
- ✅ Checks for security vulnerabilities with `gosec`
- ✅ Verifies dependencies

### Required Tools
The quality script will auto-install missing tools, but you can install them manually:
```bash
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install golang.org/x/tools/cmd/goimports@latest
go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
```

## Current Implementation Status

### What's Currently Implemented
- ✅ Basic HTTP server with secure timeouts at `cmd/api/main.go`
- ✅ Health check endpoint: `GET /health`
- ✅ Version endpoint: `GET /api/v1/version`
- ✅ Complete quality assurance pipeline via `scripts/quality.sh`
- ✅ Makefile with common development tasks
- ✅ Project structure scaffolding in `internal/` directories
- ✅ Database schema: Complete PostgreSQL schema in `migrations/000001_initial_schema.up.sql`
- ✅ sqlc configuration and generated database code in `sqlc/`
- ✅ SQL queries for user, company, registration, and password reset in `queries/`
- ✅ Core packages implemented: config, database, logger, api response handling
- ✅ Authentication foundations: JWT, password hashing, middleware structure
- ✅ Service layer scaffolding for auth, user, project, and product domains

### Next Development Steps
1. **Database Setup**: Configure PostgreSQL connection and implement sqlc queries
2. **Authentication Module**: Implement JWT-based auth in `internal/auth/`
3. **User Management**: Build user registration and management in `internal/user/`
4. **API Layer**: Expand HTTP handlers in `internal/api/`

## Security and Configuration Management

### Configuration Strategy
Follow the principle of **separation of concerns**:
- **Configuration files**: Application structure, feature flags, business logic settings
- **Environment variables**: Sensitive data (API keys, passwords, JWT secrets)
- **External stores** (Vault, AWS Secrets): Production secrets with rotation

### Environment-based Configuration
```go
type Config struct {
    Environment string `env:"ENVIRONMENT" envDefault:"development"`
    Database    DatabaseConfig `envPrefix:"DB_"`
    Security    SecurityConfig `envPrefix:"SECURITY_"`
}

// Environment-specific validation
func (c *Config) Validate() error {
    if c.Environment == "production" {
        if c.Database.SSLMode != "require" {
            return errors.New("production requires SSL database connections")
        }
        if len(c.Security.JWTSecret) < 32 {
            return errors.New("production requires strong JWT secrets")
        }
    }
    return nil
}
```

### Secret Management Best Practices
1. **Never commit secrets** to the repository
2. **Use `.env` files** only for local development (add to .gitignore)
3. **Implement key rotation** for JWT and API keys
4. **Use HTTP-only cookies** for JWT storage (XSS protection)
5. **Validate security settings** based on environment

### 12-Factor App Compliance
The project follows 12-factor app principles:
- **Config in environment**: All deployment config via env vars
- **Dependencies**: Explicitly declared in go.mod
- **Backing services**: Treat databases as attached resources
- **Build/release/run**: Strict separation of stages
- **Stateless processes**: No local session state
- **Port binding**: Self-contained HTTP server
- **Disposability**: Fast startup and graceful shutdown
- **Logs as event streams**: Structured logging with slog

## Development Notes

- Server runs on port 8080 with production-ready timeout configurations
- All Go files pass security scanning (gosec) with zero issues
- The project follows Go 1.24+ patterns including enhanced HTTP routing
- **CRITICAL**: Always run `make quality` or `./scripts/quality.sh` before committing
- Use `make tools` to install required development dependencies
- Database schema evolution should use the migrations/ directory

## Testing Philosophy

### Core Principle: "Discover abstractions, don't create them"
- Test behavior, not implementation details
- Prefer real implementations over mocks
- Use test doubles and fakes instead of mock interfaces
- Tests should understand and trust the interface-implementation boundary

### Testing Strategy
**Instead of mocks, use:**
```go
// Test doubles with controllable behavior
type FakeEmailSender struct {
    SentEmails []Email
    ShouldFail bool
}

// In-memory implementations
type InMemoryUserStore struct {
    users map[string]User
    mutex sync.RWMutex
}

// HTTP test servers for external services
server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
    json.NewEncoder(w).Encode(expectedResponse)
}))
```

### Table-Driven Tests
Use subtests for better organization and parallel execution:
```go
func TestValidation(t *testing.T) {
    tests := []struct {
        name     string
        input    string
        expected bool
    }{
        {"valid email", "<EMAIL>", true},
        {"invalid email", "not-an-email", false},
    }
    
    for _, tt := range tests {
        tt := tt // Important: capture range variable
        t.Run(tt.name, func(t *testing.T) {
            t.Parallel()
            result := ValidateEmail(tt.input)
            if result != tt.expected {
                t.Errorf("got %v, want %v", result, tt.expected)
            }
        })
    }
}
```

### Coverage Goals
- **Unit tests**: 85-95% statement coverage
- **Integration tests**: 70-80% statement coverage
- **Critical paths**: 90%+ coverage with multiple test types
- Focus on behavior coverage, not just line coverage

## Performance Analysis

### pprof Integration
```go
import _ "net/http/pprof" // Registers pprof handlers

// In main():
go func() {
    log.Println(http.ListenAndServe("localhost:6060", nil))
}()
```

### Collecting Profiles
```bash
# CPU profile (30 seconds)
curl -o cpu.pprof "http://localhost:6060/debug/pprof/profile?seconds=30"
go tool pprof cpu.pprof

# Memory profile
curl -o heap.pprof "http://localhost:6060/debug/pprof/heap"
go tool pprof -alloc_space heap.pprof  # All allocations
go tool pprof -inuse_space heap.pprof  # Current memory

# Goroutine profile
curl -o goroutines.pprof "http://localhost:6060/debug/pprof/goroutine"
```

### Profile-Guided Optimization (PGO)
Available in Go 1.21+:
```bash
# Collect production profile (6+ minutes recommended)
curl -o production.pprof "http://localhost:6060/debug/pprof/profile?seconds=360"

# Place in project root as default.pgo
cp production.pprof ./default.pgo

# Build with PGO (automatic detection)
go build

# Measure improvement
benchstat before.txt after.txt
```

### Performance Best Practices
1. **Measure first**: Use pprof to identify actual bottlenecks
2. **PGO for free gains**: 2-7% performance improvement typical
3. **Continuous profiling**: Use Pyroscope or similar in production
4. **Safe production profiling**: Implement sampling and rate limiting

## API Design Principles

### Function Naming
- Use `mixedCaps` or `MixedCaps` (not snake_case)
- Avoid stuttering with package names: `user.New()` not `user.NewUser()`
- Be consistent across the codebase

### Function Parameters
**For complex configuration, use option pattern:**
```go
type Option func(*Server)

func WithTimeout(d time.Duration) Option {
    return func(s *Server) {
        s.timeout = d
    }
}

func NewServer(opts ...Option) *Server {
    s := &Server{
        timeout: 30 * time.Second, // sensible defaults
    }
    for _, opt := range opts {
        opt(s)
    }
    return s
}
```

### Error Handling
```go
// Wrap errors with context
if err := db.Query(ctx, query); err != nil {
    return fmt.Errorf("failed to query users: %w", err)
}

// Custom error types for API responses
type ValidationError struct {
    Field   string
    Message string
}
```

### API Versioning
- Use URL path versioning: `/api/v1/`, `/api/v2/`
- Maintain backward compatibility when possible
- Document breaking changes clearly
- Consider feature flags for gradual rollouts

## Code Review Checklist

### Essential Review Steps

Before committing any new features or changes, ensure you complete these critical steps:

#### 1. **SQLc Enum Usage Review**
- ✅ Check all hardcoded enum values (status, role, type constants)
- ✅ Replace hardcoded strings with sqlc-generated enum types
- ✅ Use `constants` package for unified enum definitions
- ✅ Ensure validation functions use enum helpers (e.g., `constants.GetValidUserRoles()`)

**Example of proper enum usage:**
```go
// ❌ Bad: Hardcoded strings
validStatuses := []string{"待審", "通過", "退件"}

// ✅ Good: Use sqlc enums and constants
validStatuses := []sqlc.QuoteStatus{
    constants.QuoteStatuses.Pending,
    constants.QuoteStatuses.Approved,  
    constants.QuoteStatuses.Rejected,
}
```

#### 2. **Constants Consolidation**
- ✅ Move repeated constants to `internal/constants/constants.go`
- ✅ Use unified field length limits (e.g., `constants.UsernameMaxLength`)
- ✅ Eliminate magic numbers and hardcoded messages
- ✅ Ensure all packages import and use the constants package

#### 3. **Code Duplication Elimination**
- ✅ Use helper functions from `internal/api/helpers.go`:
  - `api.DecodeJSON()` for JSON parsing
  - `api.ParsePagination()` for query parameters
  - `api.HandleServiceError()` for error mapping
- ✅ Use authentication helpers from `internal/auth/helpers.go`:
  - `auth.RequireAuth()` for user authentication
  - `auth.RequireAdmin()` for admin-only endpoints
- ✅ Use validation helpers from `internal/validator/helpers.go`
- ✅ Use test helpers from `internal/testhelpers/helpers.go`

#### 4. **Testing Requirements**
- ✅ Achieve minimum 85% test coverage for new packages
- ✅ Use table-driven tests with subtests
- ✅ Mock dependencies using interfaces (e.g., `UserServiceInterface`)
- ✅ Test both success and error cases
- ✅ Use proper authentication context in tests:

```go
claims := &auth.Claims{
    UserID:   testUserID,
    Email:    "<EMAIL>", 
    Username: "testuser",
    Role:     constants.UserRoles.Company,
}
ctx := context.WithValue(req.Context(), auth.ContextKeyUser, claims)
```

#### 5. **Architecture Compliance**
- ✅ Follow domain-driven package structure (not MVC layers)
- ✅ Use dependency injection with interfaces
- ✅ Implement proper error handling with context
- ✅ Follow Go naming conventions (avoid stuttering)
- ✅ Use structured logging with slog

#### 6. **Quality Assurance**
- ✅ Run `make quality` to ensure all checks pass:
  - Code formatting (gofmt, goimports)
  - Static analysis (go vet)
  - Linting (golangci-lint)  
  - Security scanning (gosec)
  - Test execution with race detection
- ✅ Address any security warnings (especially G115 integer overflows)
- ✅ Ensure no circular import dependencies

#### 7. **Code Readability & Documentation**
- ✅ Add package and function comments for public APIs
- ✅ Use descriptive variable and function names
- ✅ Keep functions focused on single responsibilities
- ✅ Avoid deep nesting and complex conditional logic

### Post-Implementation Checklist

After completing a feature, verify:

1. **Functionality**: All endpoints work as expected
2. **Security**: No hardcoded credentials or sensitive data exposure
3. **Performance**: No obvious performance bottlenecks  
4. **Maintainability**: Code is clean, well-structured, and documented
5. **Testability**: Comprehensive test coverage with reliable tests
6. **Integration**: Feature integrates properly with existing codebase

### Common Anti-Patterns to Avoid

- ❌ **Magic Numbers**: Use constants instead of hardcoded values
- ❌ **String Enums**: Use sqlc-generated enum types
- ❌ **Copy-Paste Code**: Extract into helper functions
- ❌ **Giant Functions**: Break down into smaller, focused functions
- ❌ **Poor Error Messages**: Provide descriptive, actionable error messages
- ❌ **Incomplete Tests**: Test edge cases and error conditions
- ❌ **Circular Dependencies**: Design proper package relationships

### Development Mantra

> **"Every feature must be: Correct, Readable, Testable, and Maintainable"**

Always ask yourself:
1. Is this code **correct** and handles all edge cases?
2. Is this code **readable** and self-documenting?
3. Is this code **testable** with proper coverage?
4. Is this code **maintainable** and follows project conventions?

By following this checklist religiously, we ensure consistent code quality and maintainability across the entire PMS API codebase.

## Code Quality Tools Configuration

### golangci-lint Configuration
Create `.golangci.yml` for consistent linting:
```yaml
linters:
  enable:
    - gofmt
    - goimports
    - govet
    - errcheck
    - staticcheck
    - gosec
    - ineffassign
    - typecheck
    - gosimple
    - goconst
    - misspell
    - unparam
    - prealloc
    - unconvert
    - exhaustive
    - sqlclosecheck
    - bodyclose
    - noctx
    - rowserrcheck
    - stylecheck
    - exportloopref
    - goprintffuncname
    - whitespace

linters-settings:
  errcheck:
    check-type-assertions: true
    check-blank: true
  govet:
    check-shadowing: true
  gosec:
    excludes:
      - G304 # File path provided as taint input (handled by validation)
  goconst:
    min-len: 3
    min-occurrences: 3

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - gosec
        - errcheck
```

### staticcheck Configuration
Ensure comprehensive static analysis:
```bash
# Run staticcheck with all checks enabled
staticcheck -checks=all ./...

# Focus on specific check categories
staticcheck -checks="SA*" ./...  # Correctness issues
staticcheck -checks="S*" ./...   # Simplifications
staticcheck -checks="ST*" ./...  # Style issues
```

### Testing Standards

#### Unit Test Requirements
Every package must include comprehensive unit tests:
```go
// Example: Table-driven test with proper coverage
func TestUserService_Create(t *testing.T) {
    tests := []struct {
        name      string
        input     CreateUserInput
        setupMock func(*MockUserStore)
        want      *User
        wantErr   error
    }{
        {
            name: "valid user creation",
            input: CreateUserInput{
                Username: "testuser",
                Email:    "<EMAIL>",
                Password: "securepass123",
                Role:     constants.UserRoles.Company,
            },
            setupMock: func(m *MockUserStore) {
                m.EXPECT().Create(gomock.Any()).Return(&User{ID: 1}, nil)
            },
            want: &User{ID: 1},
        },
        {
            name: "duplicate username",
            input: CreateUserInput{
                Username: "existing",
            },
            setupMock: func(m *MockUserStore) {
                m.EXPECT().Create(gomock.Any()).Return(nil, ErrDuplicateUsername)
            },
            wantErr: ErrDuplicateUsername,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

#### Benchmark Tests
Add benchmarks for performance-critical code:
```go
func BenchmarkPasswordHash(b *testing.B) {
    password := "testpassword123"
    b.ResetTimer()
    
    for i := 0; i < b.N; i++ {
        _, err := HashPassword(password)
        if err != nil {
            b.Fatal(err)
        }
    }
}

// Run benchmarks with memory allocation stats
// go test -bench=. -benchmem
```

#### Fuzz Testing
Use Go's native fuzzing for input validation:
```go
func FuzzValidateEmail(f *testing.F) {
    // Add seed corpus
    f.Add("<EMAIL>")
    f.Add("invalid-email")
    f.Add("")
    
    f.Fuzz(func(t *testing.T, email string) {
        result := ValidateEmail(email)
        // Property: function should not panic
        // Property: result should be deterministic
        result2 := ValidateEmail(email)
        if result != result2 {
            t.Errorf("non-deterministic result for %q", email)
        }
    })
}

// Run fuzz tests
// go test -fuzz=FuzzValidateEmail -fuzztime=30s
```

### Continuous Quality Checks

#### Pre-commit Hook
Install a Git pre-commit hook to enforce quality:
```bash
#!/bin/bash
# .git/hooks/pre-commit

echo "Running pre-commit quality checks..."

# Run quality script
./scripts/quality.sh
if [ $? -ne 0 ]; then
    echo "Quality checks failed. Please fix issues before committing."
    exit 1
fi

echo "Quality checks passed!"
```

#### CI/CD Integration
Ensure all quality checks run in CI:
```yaml
# Example GitHub Actions workflow
name: Quality Assurance
on: [push, pull_request]

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v4
        with:
          go-version: '1.24'
      
      - name: Install tools
        run: make tools
      
      - name: Run quality checks
        run: make quality
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.out
```

### Performance Monitoring

#### Runtime Checks
Add runtime assertions for critical invariants:
```go
// Use build tags for production vs development
// +build !production

func assertInvariant(condition bool, msg string) {
    if !condition {
        panic("invariant violated: " + msg)
    }
}
```

#### Memory Leak Detection
Use runtime analysis tools:
```go
// Periodic memory stats logging
func logMemStats() {
    var m runtime.MemStats
    runtime.ReadMemStats(&m)
    
    slog.Info("Memory stats",
        "alloc_mb", m.Alloc/1024/1024,
        "total_alloc_mb", m.TotalAlloc/1024/1024,
        "sys_mb", m.Sys/1024/1024,
        "num_gc", m.NumGC,
        "goroutines", runtime.NumGoroutine(),
    )
}
```

### Code Generation Validation

Always validate generated code:
```bash
# After running sqlc generate
git diff --exit-code sqlc/
if [ $? -ne 0 ]; then
    echo "Generated code is out of sync. Run 'sqlc generate'"
    exit 1
fi
```

### Security Scanning Enhanced

#### Dependency Scanning
```bash
# Check for known vulnerabilities
go list -m all | nancy sleuth

# Or use native Go tooling
go mod audit
```

#### Secret Scanning
```bash
# Use gitleaks or similar
gitleaks detect --source . --verbose
```

### Documentation Standards

#### Package Documentation
Every package must have comprehensive godoc:
```go
// Package user provides user management functionality for the PMS API.
// It handles user registration, authentication, profile management,
// and role-based access control.
//
// The package follows domain-driven design principles with clear
// separation between business logic (service layer) and HTTP
// handling (handler layer).
package user
```

#### Example Documentation
Include runnable examples:
```go
func ExampleUserService_Create() {
    svc := NewUserService(db)
    user, err := svc.Create(ctx, CreateUserInput{
        Username: "john_doe",
        Email:    "<EMAIL>",
        Password: "secure123",
    })
    if err != nil {
        log.Fatal(err)
    }
    fmt.Printf("Created user: %s\n", user.Username)
    // Output: Created user: john_doe
}
```

## Development Best Practices Summary

1. **Always use sqlc enums** - Never hardcode status/role strings
2. **Eliminate duplication** - Use shared helpers and constants
3. **Test comprehensively** - Unit, integration, benchmark, and fuzz tests
4. **Run quality checks** - Before every commit without exception
5. **Document thoroughly** - Package, function, and example documentation
6. **Monitor performance** - Profile regularly and optimize based on data
7. **Secure by default** - Validate inputs, sanitize outputs, scan dependencies

Remember: **Quality is not negotiable**. Every line of code should be production-ready.