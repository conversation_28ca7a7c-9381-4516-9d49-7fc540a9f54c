# Gosec configuration file

# Global settings
global:
  # Audit mode - fail the scan on any finding
  audit: enabled
  # Fail on severity level (low, medium, high)
  severity: medium
  # Fail on confidence level (low, medium, high)
  confidence: medium
  # Output format (json, yaml, csv, junit-xml, html, sonarqube, golint, sarif, text)
  fmt: text
  # Verbose output
  verbose: text
  # Exclude generated files
  exclude-generated: true

# Rule specific configuration
rules:
  # G101: Look for hardcoded credentials
  G101:
    # Ignore false positives in SQL queries and constants
    ignore:
      - "password_expiration_at"
      - "password_hash"
      - "PasswordChanged"
      - "password_reset"
      - "InvalidCredentials"
    # Only check for actual secrets, not SQL column names
    pattern: '(?i)(api[_\-]?key|api[_\-]?secret|auth[_\-]?token|private[_\-]?key|secret[_\-]?key)'
  
  # G115: Potential integer overflow when converting between integer types
  G115:
    # We handle bounds checking manually where needed
    skip: false
  
  # G304: Potential file inclusion via variable
  G304:
    # We validate file paths in our application
    skip: true

# Exclude paths from scanning
exclude-dir:
  - vendor
  - .git
  - sqlc  # Generated code

# Exclude specific files
exclude:
  - "*_test.go"
  - "*.pb.go"
  - "mock_*.go"