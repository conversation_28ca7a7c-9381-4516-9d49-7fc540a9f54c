package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/koopa0/pms-api-v2/internal/config"
	"github.com/koopa0/pms-api-v2/internal/database"
	applogger "github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/server"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	// Initialize logger
	appLog := applogger.New(cfg.LogLevel)
	appLog.Info("PMS API v2 starting up",
		"environment", cfg.Env,
		"port", cfg.Server.Port)

	// Connect to database
	db, err := database.New(&cfg.Database)
	if err != nil {
		appLog.Error("Failed to connect to database", "error", err)
		os.Exit(1)
	}
	defer func() {
		if err := db.Close(); err != nil {
			appLog.Error("Failed to close database connection", "error", err)
		}
	}()

	appLog.Info("Database connection established")

	// Create API server
	server := server.NewServer(cfg, db, appLog)

	// Start server in a goroutine
	go func() {
		if err := server.Start(); err != nil {
			appLog.Error("Server failed to start", "error", err)
			os.Exit(1)
		}
	}()

	appLog.Info("Server started successfully", "port", cfg.Server.Port)

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLog.Info("Server is shutting down...")

	// Create a deadline to wait for.
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		appLog.Error("Server forced to shutdown", "error", err)
		os.Exit(1)
	}

	appLog.Info("Server exiting")
}
