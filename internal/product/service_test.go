package product

import (
	"log/slog"
	"os"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// Product package provides concrete implementations, tests verify behavior

// QueriesInterface defines the database operations needed by the product service
type QueriesInterface interface {
	// Add product-related query methods as needed
}

// MockQueries implements QueriesInterface for testing
type MockQueries struct {
	shouldFail bool
}

func NewMockQueries() *MockQueries {
	return &MockQueries{}
}

func TestNewService(t *testing.T) {
	t.Parallel()

	queries := &sqlc.Queries{}
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}

	service := NewService(queries, logger)

	if service == nil {
		t.Error("expected service to be created")
	}
	if service.queries != queries {
		t.Error("expected queries to be set")
	}
	if service.logger != logger {
		t.Error("expected logger to be set")
	}
}

func TestService_ImportProductsFromCSV(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(&sqlc.Queries{}, logger)

	tests := []struct {
		name           string
		csvData        string
		expectedResult *ImportResult
		expectError    bool
	}{
		{
			name: "valid CSV data",
			csvData: `PID,ProductVat,ProductCompany,GroupID,ProductName,Description,Category,UnitPrice,Currency
12345,12345678,Test Company,1,Test Product,Test Description,Software,1000.50,TWD
67890,87654321,Another Company,2,Another Product,Another Description,Hardware,2000.00,USD`,
			expectedResult: &ImportResult{
				SuccessCount: 2,
				FailureCount: 0,
				Errors:       nil,
			},
			expectError: false,
		},
		{
			name: "CSV with missing required fields",
			csvData: `PID,ProductVat,ProductCompany,GroupID,ProductName,Description,Category,UnitPrice,Currency
,12345678,Test Company,1,Test Product,Test Description,Software,1000.50,TWD
67890,,Another Company,2,Another Product,Another Description,Hardware,2000.00,USD`,
			expectedResult: &ImportResult{
				SuccessCount: 0,
				FailureCount: 2,
				Errors: []string{
					"line 2: PID is required",
					"line 3: ProductVat is required",
				},
			},
			expectError: false,
		},
		{
			name: "CSV with invalid numeric fields",
			csvData: `PID,ProductVat,ProductCompany,GroupID,ProductName,Description,Category,UnitPrice,Currency
invalid,12345678,Test Company,1,Test Product,Test Description,Software,1000.50,TWD
67890,87654321,Another Company,invalid,Another Product,Another Description,Hardware,invalid,USD`,
			expectedResult: &ImportResult{
				SuccessCount: 0,
				FailureCount: 2,
				Errors: []string{
					"line 2: PID must be a valid number",
					"line 3: GroupID must be a valid number",
				},
			},
			expectError: false,
		},
		{
			name: "CSV with invalid header",
			csvData: `WrongHeader,ProductVat,ProductCompany,GroupID,ProductName,Description,Category,UnitPrice,Currency
12345,12345678,Test Company,1,Test Product,Test Description,Software,1000.50,TWD`,
			expectedResult: nil,
			expectError:    true,
		},
		{
			name:           "empty CSV data",
			csvData:        "",
			expectedResult: nil,
			expectError:    true,
		},
		{
			name: "CSV with optional fields",
			csvData: `PID,ProductVat,ProductCompany,GroupID,ProductName,Description,Category,UnitPrice,Currency
12345,12345678,Test Company,1,Test Product,,,1000.50,
67890,87654321,Another Company,2,Another Product,Description,Category,,TWD`,
			expectedResult: &ImportResult{
				SuccessCount: 2,
				FailureCount: 0,
				Errors:       nil,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := testutils.CreateTestContext()
			result, err := service.ImportProductsFromCSV(ctx, []byte(tt.csvData))

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if result == nil {
				t.Error("expected result but got nil")
				return
			}

			if result.SuccessCount != tt.expectedResult.SuccessCount {
				t.Errorf("expected SuccessCount %d, got %d", tt.expectedResult.SuccessCount, result.SuccessCount)
			}

			if result.FailureCount != tt.expectedResult.FailureCount {
				t.Errorf("expected FailureCount %d, got %d", tt.expectedResult.FailureCount, result.FailureCount)
			}

			if len(result.Errors) != len(tt.expectedResult.Errors) {
				t.Errorf("expected %d errors, got %d", len(tt.expectedResult.Errors), len(result.Errors))
			}
		})
	}
}

func TestService_GetProductByID(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(&sqlc.Queries{}, logger)

	tests := []struct {
		name        string
		productID   int32
		expectError bool
	}{
		{
			name:        "valid product ID",
			productID:   1,
			expectError: false,
		},
		{
			name:        "another valid product ID",
			productID:   999,
			expectError: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := testutils.CreateTestContext()
			product, err := service.GetProductByID(ctx, tt.productID)

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if product == nil {
				t.Error("expected product but got nil")
				return
			}

			if product.ID != tt.productID {
				t.Errorf("expected product ID %d, got %d", tt.productID, product.ID)
			}

			// Verify required fields are set
			if product.ProductName == "" {
				t.Error("expected ProductName to be set")
			}
			if product.ProductVat == "" {
				t.Error("expected ProductVat to be set")
			}
			if product.ProductCompany == "" {
				t.Error("expected ProductCompany to be set")
			}
		})
	}
}

func TestService_ListProducts(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(&sqlc.Queries{}, logger)

	tests := []struct {
		name        string
		request     ListProductsRequest
		expectError bool
	}{
		{
			name: "basic list request",
			request: ListProductsRequest{
				Pagination: types.PaginationParams{
					Page:     1,
					PageSize: 20,
				},
			},
			expectError: false,
		},
		{
			name: "list with filters",
			request: ListProductsRequest{
				CompanyVat: "12345678",
				Category:   "Software",
				IsActive:   boolPtr(true),
				Pagination: types.PaginationParams{
					Page:     1,
					PageSize: 10,
				},
			},
			expectError: false,
		},
		{
			name: "list with inactive filter",
			request: ListProductsRequest{
				IsActive: boolPtr(false),
				Pagination: types.PaginationParams{
					Page:     2,
					PageSize: 5,
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := testutils.CreateTestContext()
			response, err := service.ListProducts(ctx, tt.request)

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if response == nil {
				t.Error("expected response but got nil")
				return
			}

			// Verify pagination structure
			if response.Data == nil {
				t.Error("expected Data to be set")
			}

			if response.Pagination.Page != tt.request.Pagination.Page {
				t.Errorf("expected page %d, got %d", tt.request.Pagination.Page, response.Pagination.Page)
			}

			if response.Pagination.PageSize != tt.request.Pagination.PageSize {
				t.Errorf("expected page size %d, got %d", tt.request.Pagination.PageSize, response.Pagination.PageSize)
			}
		})
	}
}

func TestService_HelperMethods(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(&sqlc.Queries{}, logger)

	t.Run("validateCSVHeader", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name     string
			header   []string
			expected []string
			want     bool
		}{
			{
				name:     "valid header",
				header:   []string{"PID", "ProductVat", "ProductCompany"},
				expected: []string{"PID", "ProductVat", "ProductCompany"},
				want:     true,
			},
			{
				name:     "invalid header order",
				header:   []string{"ProductVat", "PID", "ProductCompany"},
				expected: []string{"PID", "ProductVat", "ProductCompany"},
				want:     false,
			},
			{
				name:     "missing header field",
				header:   []string{"PID", "ProductVat"},
				expected: []string{"PID", "ProductVat", "ProductCompany"},
				want:     false,
			},
			{
				name:     "extra header field",
				header:   []string{"PID", "ProductVat", "ProductCompany", "Extra"},
				expected: []string{"PID", "ProductVat", "ProductCompany"},
				want:     false,
			},
			{
				name:     "header with whitespace",
				header:   []string{" PID ", " ProductVat ", " ProductCompany "},
				expected: []string{"PID", "ProductVat", "ProductCompany"},
				want:     true,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				result := service.validateCSVHeader(tt.header, tt.expected)
				if result != tt.want {
					t.Errorf("expected %v, got %v", tt.want, result)
				}
			})
		}
	})

	t.Run("parseCSVRecord", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name     string
			record   []string
			expected CSVProductRecord
		}{
			{
				name: "complete record",
				record: []string{
					"12345", "12345678", "Test Company", "1", "Test Product",
					"Description", "Category", "1000.50", "TWD",
				},
				expected: CSVProductRecord{
					PID:            "12345",
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "1",
					ProductName:    "Test Product",
					Description:    "Description",
					Category:       "Category",
					UnitPrice:      "1000.50",
					Currency:       "TWD",
				},
			},
			{
				name: "record with whitespace",
				record: []string{
					" 12345 ", " 12345678 ", " Test Company ", " 1 ", " Test Product ",
					" Description ", " Category ", " 1000.50 ", " TWD ",
				},
				expected: CSVProductRecord{
					PID:            "12345",
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "1",
					ProductName:    "Test Product",
					Description:    "Description",
					Category:       "Category",
					UnitPrice:      "1000.50",
					Currency:       "TWD",
				},
			},
			{
				name: "incomplete record",
				record: []string{"12345", "12345678", "Test Company"},
				expected: CSVProductRecord{
					PID:            "12345",
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "",
					ProductName:    "",
					Description:    "",
					Category:       "",
					UnitPrice:      "",
					Currency:       "",
				},
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				result := service.parseCSVRecord(tt.record)
				if result != tt.expected {
					t.Errorf("expected %+v, got %+v", tt.expected, result)
				}
			})
		}
	})

	t.Run("validateCSVRecord", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			record      CSVProductRecord
			lineNumber  int
			expectError bool
			errorMsg    string
		}{
			{
				name: "valid record",
				record: CSVProductRecord{
					PID:            "12345",
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "1",
					ProductName:    "Test Product",
					UnitPrice:      "1000.50",
				},
				lineNumber:  2,
				expectError: false,
			},
			{
				name: "missing PID",
				record: CSVProductRecord{
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "1",
					ProductName:    "Test Product",
				},
				lineNumber:  2,
				expectError: true,
				errorMsg:    "line 2: PID is required",
			},
			{
				name: "invalid PID",
				record: CSVProductRecord{
					PID:            "invalid",
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "1",
					ProductName:    "Test Product",
				},
				lineNumber:  3,
				expectError: true,
				errorMsg:    "line 3: PID must be a valid number",
			},
			{
				name: "invalid GroupID",
				record: CSVProductRecord{
					PID:            "12345",
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "invalid",
					ProductName:    "Test Product",
				},
				lineNumber:  4,
				expectError: true,
				errorMsg:    "line 4: GroupID must be a valid number",
			},
			{
				name: "invalid UnitPrice",
				record: CSVProductRecord{
					PID:            "12345",
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "1",
					ProductName:    "Test Product",
					UnitPrice:      "invalid",
				},
				lineNumber:  5,
				expectError: true,
				errorMsg:    "line 5: UnitPrice must be a valid number",
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				err := service.validateCSVRecord(tt.record, tt.lineNumber)

				if tt.expectError {
					if err == nil {
						t.Error("expected error but got none")
						return
					}
					if err.Error() != tt.errorMsg {
						t.Errorf("expected error message %q, got %q", tt.errorMsg, err.Error())
					}
				} else {
					if err != nil {
						t.Errorf("unexpected error: %v", err)
					}
				}
			})
		}
	})

	t.Run("convertCSVToProduct", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name     string
			record   CSVProductRecord
			expected *ProductInfo
		}{
			{
				name: "complete record",
				record: CSVProductRecord{
					PID:            "12345",
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        "1",
					ProductName:    "Test Product",
					Description:    "Test Description",
					Category:       "Software",
					UnitPrice:      "1000.50",
					Currency:       "TWD",
				},
				expected: &ProductInfo{
					PID:            12345,
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        1,
					ProductName:    "Test Product",
					Description:    stringPtr("Test Description"),
					Category:       stringPtr("Software"),
					UnitPrice:      float64Ptr(1000.50),
					Currency:       stringPtr("TWD"),
					IsActive:       true,
				},
			},
			{
				name: "minimal record",
				record: CSVProductRecord{
					PID:            "67890",
					ProductVat:     "87654321",
					ProductCompany: "Another Company",
					GroupID:        "2",
					ProductName:    "Another Product",
				},
				expected: &ProductInfo{
					PID:            67890,
					ProductVat:     "87654321",
					ProductCompany: "Another Company",
					GroupID:        2,
					ProductName:    "Another Product",
					IsActive:       true,
				},
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				result, err := service.convertCSVToProduct(tt.record)
				if err != nil {
					t.Errorf("unexpected error: %v", err)
					return
				}

				if result.PID != tt.expected.PID {
					t.Errorf("expected PID %d, got %d", tt.expected.PID, result.PID)
				}
				if result.ProductVat != tt.expected.ProductVat {
					t.Errorf("expected ProductVat %s, got %s", tt.expected.ProductVat, result.ProductVat)
				}
				if result.ProductCompany != tt.expected.ProductCompany {
					t.Errorf("expected ProductCompany %s, got %s", tt.expected.ProductCompany, result.ProductCompany)
				}
				if result.GroupID != tt.expected.GroupID {
					t.Errorf("expected GroupID %d, got %d", tt.expected.GroupID, result.GroupID)
				}
				if result.ProductName != tt.expected.ProductName {
					t.Errorf("expected ProductName %s, got %s", tt.expected.ProductName, result.ProductName)
				}
				if result.IsActive != tt.expected.IsActive {
					t.Errorf("expected IsActive %v, got %v", tt.expected.IsActive, result.IsActive)
				}

				// Check optional fields
				if tt.expected.Description != nil {
					if result.Description == nil || *result.Description != *tt.expected.Description {
						t.Errorf("expected Description %v, got %v", tt.expected.Description, result.Description)
					}
				}
				if tt.expected.Category != nil {
					if result.Category == nil || *result.Category != *tt.expected.Category {
						t.Errorf("expected Category %v, got %v", tt.expected.Category, result.Category)
					}
				}
				if tt.expected.UnitPrice != nil {
					if result.UnitPrice == nil || *result.UnitPrice != *tt.expected.UnitPrice {
						t.Errorf("expected UnitPrice %v, got %v", tt.expected.UnitPrice, result.UnitPrice)
					}
				}
				if tt.expected.Currency != nil {
					if result.Currency == nil || *result.Currency != *tt.expected.Currency {
						t.Errorf("expected Currency %v, got %v", tt.expected.Currency, result.Currency)
					}
				}
			})
		}
	})
}

func TestServiceErrors(t *testing.T) {
	t.Parallel()

	// Test that error constants are defined
	if ErrProductNotFound == nil {
		t.Error("expected ErrProductNotFound to be defined")
	}
	if ErrInvalidCSV == nil {
		t.Error("expected ErrInvalidCSV to be defined")
	}
	if ErrInvalidInput == nil {
		t.Error("expected ErrInvalidInput to be defined")
	}
	if ErrInternalError == nil {
		t.Error("expected ErrInternalError to be defined")
	}

	// Test error messages
	if ErrProductNotFound.Error() == "" {
		t.Error("expected ErrProductNotFound to have a message")
	}
	if ErrInvalidCSV.Error() == "" {
		t.Error("expected ErrInvalidCSV to have a message")
	}
	if ErrInvalidInput.Error() == "" {
		t.Error("expected ErrInvalidInput to have a message")
	}
	if ErrInternalError.Error() == "" {
		t.Error("expected ErrInternalError to have a message")
	}
}

// Helper function to create bool pointer
func boolPtr(b bool) *bool {
	return &b
}
