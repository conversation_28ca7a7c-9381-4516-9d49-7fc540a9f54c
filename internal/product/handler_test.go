package product

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/internal/types"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// <PERSON>ler tests define interfaces for mocking service dependencies

// ProductServiceInterface defines the service operations needed by the handler
type ProductServiceInterface interface {
	ImportProductsFromCSV(ctx context.Context, csvData []byte) (*ImportResult, error)
	GetProductByID(ctx context.Context, productID int32) (*ProductInfo, error)
	ListProducts(ctx context.Context, req ListProductsRequest) (*ListProductsResponse, error)
}

// MockProductService implements ProductServiceInterface for testing
type MockProductService struct {
	importResult    *ImportResult
	importError     error
	product         *ProductInfo
	getError        error
	listResponse    *ListProductsResponse
	listError       error
	shouldFailImport bool
	shouldFailGet    bool
	shouldFailList   bool
}

func NewMockProductService() *MockProductService {
	return &MockProductService{
		importResult: &ImportResult{
			SuccessCount: 1,
			FailureCount: 0,
		},
		product: &ProductInfo{
			ID:             1,
			PID:            12345,
			ProductVat:     "12345678",
			ProductCompany: "Test Company",
			GroupID:        1,
			ProductName:    "Test Product",
			IsActive:       true,
		},
		listResponse: &ListProductsResponse{
			Data: []ProductInfo{
				{
					ID:             1,
					PID:            12345,
					ProductVat:     "12345678",
					ProductCompany: "Test Company",
					GroupID:        1,
					ProductName:    "Test Product",
					IsActive:       true,
				},
			},
			Pagination: types.PaginationMeta{
				Page:       1,
				PageSize:   20,
				Total:      1,
				TotalPages: 1,
				HasNext:    false,
				HasPrevious: false,
			},
		},
	}
}

func (m *MockProductService) ImportProductsFromCSV(ctx context.Context, csvData []byte) (*ImportResult, error) {
	if m.shouldFailImport {
		return nil, m.importError
	}
	return m.importResult, nil
}

func (m *MockProductService) GetProductByID(ctx context.Context, productID int32) (*ProductInfo, error) {
	if m.shouldFailGet {
		return nil, m.getError
	}
	// Update the product ID to match the request
	product := *m.product
	product.ID = productID
	return &product, nil
}

func (m *MockProductService) ListProducts(ctx context.Context, req ListProductsRequest) (*ListProductsResponse, error) {
	if m.shouldFailList {
		return nil, m.listError
	}
	return m.listResponse, nil
}

func TestNewHandler(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}

	handler := NewHandler(&Service{}, logger)

	if handler == nil {
		t.Error("expected handler to be created")
	}
	if handler.logger != logger {
		t.Error("expected logger to be set")
	}
}

func TestHandler_ImportProducts(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		contentType    string
		csvData        string
		setupMock      func(*MockProductService)
		expectedStatus int
		expectError    bool
	}{
		{
			name:        "successful CSV import",
			contentType: "text/csv",
			csvData: `PID,ProductVat,ProductCompany,GroupID,ProductName,Description,Category,UnitPrice,Currency
12345,12345678,Test Company,1,Test Product,Description,Category,1000.50,TWD`,
			setupMock: func(m *MockProductService) {
				m.importResult = &ImportResult{
					SuccessCount: 1,
					FailureCount: 0,
				}
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:        "successful CSV import with application/csv",
			contentType: "application/csv",
			csvData: `PID,ProductVat,ProductCompany,GroupID,ProductName,Description,Category,UnitPrice,Currency
12345,12345678,Test Company,1,Test Product,Description,Category,1000.50,TWD`,
			setupMock: func(m *MockProductService) {
				m.importResult = &ImportResult{
					SuccessCount: 1,
					FailureCount: 0,
				}
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "invalid content type",
			contentType:    "application/json",
			csvData:        "some data",
			setupMock:      func(m *MockProductService) {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:           "empty CSV data",
			contentType:    "text/csv",
			csvData:        "",
			setupMock:      func(m *MockProductService) {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:        "service error",
			contentType: "text/csv",
			csvData:     "some csv data",
			setupMock: func(m *MockProductService) {
				m.shouldFailImport = true
				m.importError = ErrInvalidCSV
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create test service that implements the interface
			mockService := NewMockProductService()
			tt.setupMock(mockService)

			// Create a test service wrapper
			testService := &TestProductService{service: mockService}
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}

			// Create a test handler that uses our mock service
			handler := &TestHandler{
				service: testService,
				logger:  logger,
			}

			// Create request
			req := httptest.NewRequest(http.MethodPost, "/api/v1/products/import", strings.NewReader(tt.csvData))
			req.Header.Set("Content-Type", tt.contentType)
			req = req.WithContext(testutils.CreateTestContext())

			// Create response recorder
			w := httptest.NewRecorder()

			// Call handler
			handler.ImportProducts(w, req)

			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Check response body for success cases
			if !tt.expectError {
				var response map[string]interface{}
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("failed to parse response JSON: %v", err)
				}

				if response["success"] != true {
					t.Error("expected success to be true")
				}

				if response["data"] == nil {
					t.Error("expected data to be present")
				}
			}
		})
	}
}

func TestHandler_GetProduct(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		productID      string
		setupMock      func(*MockProductService)
		expectedStatus int
		expectError    bool
	}{
		{
			name:      "successful get product",
			productID: "1",
			setupMock: func(m *MockProductService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "invalid product ID",
			productID:      "invalid",
			setupMock:      func(m *MockProductService) {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:      "service error",
			productID: "1",
			setupMock: func(m *MockProductService) {
				m.shouldFailGet = true
				m.getError = ErrProductNotFound
			},
			expectedStatus: http.StatusNotFound,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create test service
			mockService := NewMockProductService()
			tt.setupMock(mockService)

			testService := &TestProductService{service: mockService}
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := &TestHandler{
				service: testService,
				logger:  logger,
			}

			// Create request with path parameter
			req := httptest.NewRequest(http.MethodGet, "/api/v1/products/"+tt.productID, nil)
			req = req.WithContext(testutils.CreateTestContext())

			// Add the ID to the request context (simulating router behavior)
			if tt.productID != "invalid" {
				// This would normally be done by the router
				req = req.WithContext(context.WithValue(req.Context(), "id", tt.productID))
			}

			w := httptest.NewRecorder()

			// Call handler
			handler.GetProduct(w, req)

			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Check response for success cases
			if !tt.expectError {
				var response map[string]interface{}
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("failed to parse response JSON: %v", err)
				}

				if response["success"] != true {
					t.Error("expected success to be true")
				}

				if response["data"] == nil {
					t.Error("expected data to be present")
				}
			}
		})
	}
}

// TestProductService wraps the mock service to implement the Service interface
type TestProductService struct {
	service ProductServiceInterface
}

func (t *TestProductService) ImportProductsFromCSV(ctx context.Context, csvData []byte) (*ImportResult, error) {
	return t.service.ImportProductsFromCSV(ctx, csvData)
}

func (t *TestProductService) GetProductByID(ctx context.Context, productID int32) (*ProductInfo, error) {
	return t.service.GetProductByID(ctx, productID)
}

func (t *TestProductService) ListProducts(ctx context.Context, req ListProductsRequest) (*ListProductsResponse, error) {
	return t.service.ListProducts(ctx, req)
}

// TestHandler wraps the test service to implement handler methods
type TestHandler struct {
	service ProductServiceInterface
	logger  *logger.Logger
}

func (h *TestHandler) ImportProducts(w http.ResponseWriter, r *http.Request) {
	// Simplified version of the real handler for testing
	contentType := r.Header.Get("Content-Type")
	if contentType != "text/csv" && contentType != "application/csv" {
		http.Error(w, "invalid content type", http.StatusBadRequest)
		return
	}

	csvData, err := io.ReadAll(r.Body)
	if err != nil || len(csvData) == 0 {
		http.Error(w, "invalid CSV data", http.StatusBadRequest)
		return
	}

	result, err := h.service.ImportProductsFromCSV(r.Context(), csvData)
	if err != nil {
		if err == ErrInvalidCSV {
			http.Error(w, err.Error(), http.StatusBadRequest)
		} else {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"data":    result,
	})
}

func (h *TestHandler) GetProduct(w http.ResponseWriter, r *http.Request) {
	// Simplified version for testing
	idStr := r.Context().Value("id")
	if idStr == nil {
		http.Error(w, "missing id parameter", http.StatusBadRequest)
		return
	}

	id, ok := idStr.(string)
	if !ok {
		http.Error(w, "invalid id parameter", http.StatusBadRequest)
		return
	}

	productID, err := strconv.ParseInt(id, 10, 32)
	if err != nil {
		http.Error(w, "invalid id parameter", http.StatusBadRequest)
		return
	}

	product, err := h.service.GetProductByID(r.Context(), int32(productID))
	if err != nil {
		if err == ErrProductNotFound {
			http.Error(w, err.Error(), http.StatusNotFound)
		} else {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"data":    product,
	})
}

func (h *TestHandler) ListProducts(w http.ResponseWriter, r *http.Request) {
	// Simplified version for testing
	req := ListProductsRequest{
		CompanyVat: r.URL.Query().Get("company_vat"),
		Category:   r.URL.Query().Get("category"),
		Pagination: types.PaginationParams{
			Page:     1,
			PageSize: 20,
		},
	}

	// Parse is_active parameter
	if isActiveStr := r.URL.Query().Get("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			req.IsActive = &isActive
		}
	}

	products, err := h.service.ListProducts(r.Context(), req)
	if err != nil {
		if err == ErrInternalError {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		} else {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"data":    products,
	})
}

func (h *TestHandler) ImportProductsJSON(w http.ResponseWriter, r *http.Request) {
	var req ImportProductsRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "invalid JSON", http.StatusBadRequest)
		return
	}

	if len(req.CSVData) == 0 {
		http.Error(w, "CSV data is required", http.StatusBadRequest)
		return
	}

	result, err := h.service.ImportProductsFromCSV(r.Context(), req.CSVData)
	if err != nil {
		if err == ErrInvalidCSV {
			http.Error(w, err.Error(), http.StatusBadRequest)
		} else {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"data":    result,
	})
}

func TestHandler_ListProducts(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		queryParams    string
		setupMock      func(*MockProductService)
		expectedStatus int
		expectError    bool
	}{
		{
			name:        "successful list products",
			queryParams: "?page=1&page_size=20",
			setupMock: func(m *MockProductService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:        "list with filters",
			queryParams: "?company_vat=12345678&category=Software&is_active=true&page=1&page_size=10",
			setupMock: func(m *MockProductService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:        "list with invalid is_active parameter",
			queryParams: "?is_active=invalid&page=1&page_size=10",
			setupMock: func(m *MockProductService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:        "service error",
			queryParams: "?page=1&page_size=20",
			setupMock: func(m *MockProductService) {
				m.shouldFailList = true
				m.listError = ErrInternalError
			},
			expectedStatus: http.StatusInternalServerError,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create test service
			mockService := NewMockProductService()
			tt.setupMock(mockService)

			testService := &TestProductService{service: mockService}
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := &TestHandler{
				service: testService,
				logger:  logger,
			}

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/v1/products"+tt.queryParams, nil)
			req = req.WithContext(testutils.CreateTestContext())

			w := httptest.NewRecorder()

			// Call handler
			handler.ListProducts(w, req)

			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Check response for success cases
			if !tt.expectError {
				var response map[string]interface{}
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("failed to parse response JSON: %v", err)
				}

				if response["success"] != true {
					t.Error("expected success to be true")
				}

				if response["data"] == nil {
					t.Error("expected data to be present")
				}

				// Check pagination structure
				data, ok := response["data"].(map[string]interface{})
				if !ok {
					t.Error("expected data to be an object")
					return
				}

				if data["pagination"] == nil {
					t.Error("expected pagination to be present")
				}
			}
		})
	}
}

func TestHandler_ImportProductsJSON(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    interface{}
		setupMock      func(*MockProductService)
		expectedStatus int
		expectError    bool
	}{
		{
			name: "successful JSON import",
			requestBody: ImportProductsRequest{
				CSVData: []byte(`PID,ProductVat,ProductCompany,GroupID,ProductName,Description,Category,UnitPrice,Currency
12345,12345678,Test Company,1,Test Product,Description,Category,1000.50,TWD`),
			},
			setupMock: func(m *MockProductService) {
				m.importResult = &ImportResult{
					SuccessCount: 1,
					FailureCount: 0,
				}
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name: "empty CSV data",
			requestBody: ImportProductsRequest{
				CSVData: []byte{},
			},
			setupMock:      func(m *MockProductService) {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:           "invalid JSON",
			requestBody:    "invalid json",
			setupMock:      func(m *MockProductService) {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "service error",
			requestBody: ImportProductsRequest{
				CSVData: []byte("some csv data"),
			},
			setupMock: func(m *MockProductService) {
				m.shouldFailImport = true
				m.importError = ErrInvalidCSV
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create test service
			mockService := NewMockProductService()
			tt.setupMock(mockService)

			testService := &TestProductService{service: mockService}
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := &TestHandler{
				service: testService,
				logger:  logger,
			}

			// Create request body
			var body []byte
			var err error
			if str, ok := tt.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				if err != nil {
					t.Fatalf("failed to marshal request body: %v", err)
				}
			}

			// Create request
			req := httptest.NewRequest(http.MethodPost, "/api/v1/products/import-json", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")
			req = req.WithContext(testutils.CreateTestContext())

			w := httptest.NewRecorder()

			// Call handler
			handler.ImportProductsJSON(w, req)

			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Check response for success cases
			if !tt.expectError {
				var response map[string]interface{}
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("failed to parse response JSON: %v", err)
				}

				if response["success"] != true {
					t.Error("expected success to be true")
				}

				if response["data"] == nil {
					t.Error("expected data to be present")
				}
			}
		})
	}
}
