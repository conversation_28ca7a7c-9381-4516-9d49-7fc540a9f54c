package product

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"

	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Service provides product management operations
type Service struct {
	queries *sqlc.Queries
	logger  *logger.Logger
}

// NewService creates a new product service
func NewService(queries *sqlc.Queries, logger *logger.Logger) *Service {
	return &Service{
		queries: queries,
		logger:  logger,
	}
}

// ProductInfo represents product information
type ProductInfo struct {
	ID             int32    `json:"id"`
	PID            int32    `json:"pid"`
	ProductVat     string   `json:"product_vat"`
	ProductCompany string   `json:"product_company"`
	GroupID        int32    `json:"group_id"`
	ProductName    string   `json:"product_name"`
	Description    *string  `json:"description,omitempty"`
	Category       *string  `json:"category,omitempty"`
	UnitPrice      *float64 `json:"unit_price,omitempty"`
	Currency       *string  `json:"currency,omitempty"`
	IsActive       bool     `json:"is_active"`
}

// ImportProductsRequest represents product import parameters
type ImportProductsRequest struct {
	CSVData []byte `json:"csv_data"`
}

// ImportResult represents import operation result
type ImportResult struct {
	SuccessCount int      `json:"success_count"`
	FailureCount int      `json:"failure_count"`
	Errors       []string `json:"errors,omitempty"`
}

// ListProductsRequest represents product listing parameters
type ListProductsRequest struct {
	CompanyVat string                 `json:"company_vat"`
	Category   string                 `json:"category"`
	IsActive   *bool                  `json:"is_active"`
	Pagination types.PaginationParams `json:"pagination"`
}

// ListProductsResponse represents product listing response
type ListProductsResponse = types.PaginatedResponse[ProductInfo]

// CSVProductRecord represents a product record from CSV
type CSVProductRecord struct {
	PID            string
	ProductVat     string
	ProductCompany string
	GroupID        string
	ProductName    string
	Description    string
	Category       string
	UnitPrice      string
	Currency       string
}

// ImportProductsFromCSV imports products from CSV data
func (s *Service) ImportProductsFromCSV(ctx context.Context, csvData []byte) (*ImportResult, error) {
	reader := csv.NewReader(strings.NewReader(string(csvData)))

	// Read header
	header, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV header: %w", err)
	}

	// Validate header
	expectedHeaders := []string{"PID", "ProductVat", "ProductCompany", "GroupID", "ProductName", "Description", "Category", "UnitPrice", "Currency"}
	if !s.validateCSVHeader(header, expectedHeaders) {
		return nil, fmt.Errorf("invalid CSV header, expected: %v", expectedHeaders)
	}

	result := &ImportResult{}
	lineNumber := 1 // Start from 1 since header is line 0

	for {
		lineNumber++
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			result.FailureCount++
			result.Errors = append(result.Errors, fmt.Sprintf("line %d: failed to read CSV record: %v", lineNumber, err))
			continue
		}

		csvRecord := s.parseCSVRecord(record)
		if err := s.validateCSVRecord(csvRecord, lineNumber); err != nil {
			result.FailureCount++
			result.Errors = append(result.Errors, err.Error())
			continue
		}

		// Convert and create product
		product, err := s.convertCSVToProduct(csvRecord)
		if err != nil {
			result.FailureCount++
			result.Errors = append(result.Errors, fmt.Sprintf("line %d: %v", lineNumber, err))
			continue
		}

		// Here we would normally call a CreateProduct query
		// For now, we'll just log and count as success
		s.logger.Info("product imported successfully",
			"pid", product.PID,
			"name", product.ProductName,
			"company", product.ProductCompany)

		result.SuccessCount++
	}

	return result, nil
}

// GetProductByID retrieves product by ID
func (s *Service) GetProductByID(ctx context.Context, productID int32) (*ProductInfo, error) {
	// Mock implementation - would use actual SQL query
	product := &ProductInfo{
		ID:             productID,
		PID:            12345,
		ProductVat:     "12345678",
		ProductCompany: "測試公司",
		GroupID:        1,
		ProductName:    "測試產品",
		IsActive:       true,
	}

	return product, nil
}

// ListProducts retrieves products with pagination and filters
func (s *Service) ListProducts(ctx context.Context, req ListProductsRequest) (*ListProductsResponse, error) {
	// Mock response for now
	products := []ProductInfo{
		{
			ID:             1,
			PID:            12345,
			ProductVat:     "12345678",
			ProductCompany: "測試軟體公司",
			GroupID:        1,
			ProductName:    "企業級軟體解決方案",
			Category:       types.StringPtr("軟體產品"),
			UnitPrice:      float64Ptr(100000.0),
			Currency:       types.StringPtr("TWD"),
			IsActive:       true,
		},
	}

	// Create paginated response using helper
	response := types.NewPaginatedResponse(products, req.Pagination, len(products))
	return &response, nil
}

// Helper methods

func (s *Service) validateCSVHeader(header, expected []string) bool {
	if len(header) != len(expected) {
		return false
	}
	for i, h := range header {
		if strings.TrimSpace(h) != expected[i] {
			return false
		}
	}
	return true
}

func (s *Service) parseCSVRecord(record []string) CSVProductRecord {
	// Ensure we have enough fields
	for len(record) < 9 {
		record = append(record, "")
	}

	return CSVProductRecord{
		PID:            strings.TrimSpace(record[0]),
		ProductVat:     strings.TrimSpace(record[1]),
		ProductCompany: strings.TrimSpace(record[2]),
		GroupID:        strings.TrimSpace(record[3]),
		ProductName:    strings.TrimSpace(record[4]),
		Description:    strings.TrimSpace(record[5]),
		Category:       strings.TrimSpace(record[6]),
		UnitPrice:      strings.TrimSpace(record[7]),
		Currency:       strings.TrimSpace(record[8]),
	}
}

func (s *Service) validateCSVRecord(record CSVProductRecord, lineNumber int) error {
	if record.PID == "" {
		return fmt.Errorf("line %d: PID is required", lineNumber)
	}
	if record.ProductVat == "" {
		return fmt.Errorf("line %d: ProductVat is required", lineNumber)
	}
	if record.ProductCompany == "" {
		return fmt.Errorf("line %d: ProductCompany is required", lineNumber)
	}
	if record.GroupID == "" {
		return fmt.Errorf("line %d: GroupID is required", lineNumber)
	}
	if record.ProductName == "" {
		return fmt.Errorf("line %d: ProductName is required", lineNumber)
	}

	// Validate PID is numeric
	if _, err := strconv.ParseInt(record.PID, 10, 32); err != nil {
		return fmt.Errorf("line %d: PID must be a valid number", lineNumber)
	}

	// Validate GroupID is numeric
	if _, err := strconv.ParseInt(record.GroupID, 10, 32); err != nil {
		return fmt.Errorf("line %d: GroupID must be a valid number", lineNumber)
	}

	// Validate UnitPrice if provided
	if record.UnitPrice != "" {
		if _, err := strconv.ParseFloat(record.UnitPrice, 64); err != nil {
			return fmt.Errorf("line %d: UnitPrice must be a valid number", lineNumber)
		}
	}

	return nil
}

func (s *Service) convertCSVToProduct(record CSVProductRecord) (*ProductInfo, error) {
	pid, _ := strconv.ParseInt(record.PID, 10, 32)
	groupID, _ := strconv.ParseInt(record.GroupID, 10, 32)

	product := &ProductInfo{
		PID:            int32(pid),
		ProductVat:     record.ProductVat,
		ProductCompany: record.ProductCompany,
		GroupID:        int32(groupID),
		ProductName:    record.ProductName,
		IsActive:       true,
	}

	if record.Description != "" {
		product.Description = &record.Description
	}
	if record.Category != "" {
		product.Category = &record.Category
	}
	if record.UnitPrice != "" {
		if price, err := strconv.ParseFloat(record.UnitPrice, 64); err == nil {
			product.UnitPrice = &price
		}
	}
	if record.Currency != "" {
		product.Currency = &record.Currency
	}

	return product, nil
}

// Utility functions
func stringPtr(s string) *string {
	return &s
}

func float64Ptr(f float64) *float64 {
	return &f
}

// Service errors
var (
	ErrProductNotFound = errors.New("product not found")
	ErrInvalidCSV      = errors.New("invalid CSV format")
	ErrInvalidInput    = errors.New("invalid input")
	ErrInternalError   = errors.New("internal server error")
)
