package api

import (
	"net/http"
	"net/url"
	"strings"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/testutils"
)

func TestDecodeJSONRequest(t *testing.T) {
	t.Parallel()

	tests := []testutils.HTTPTestCase{
		{
			Name:   "valid JSON",
			Method: "POST",
			URL:    "/test",
			Body: map[string]interface{}{
				"name":  "test",
				"value": 42,
			},
			ExpectedStatus: 200,
			ValidateFunc: func(t *testing.T, response map[string]interface{}) {
				// Custom validation would go here
			},
		},
		{
			Name:           "invalid JSON",
			Method:         "POST",
			URL:            "/test",
			Body:           `{"invalid": json}`,
			ExpectedStatus: 400,
			ExpectedError:  "Invalid JSON format",
		},
		{
			Name:           "empty body",
			Method:         "POST",
			URL:            "/test",
			Body:           nil,
			ExpectedStatus: 400,
			ExpectedError:  "Invalid JSON format",
		},
	}

	handler := func(w http.ResponseWriter, r *http.Request) {
		var data map[string]interface{}
		if DecodeJSONRequest(w, r, &data) {
			Success(w, data)
		}
		// Error response is handled by DecodeJSONRequest
	}

	testutils.RunHTTPTestCases(t, handler, tests)
}

func TestParseIDParam(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		pathValue      string
		paramName      string
		expectedID     int32
		expectedOK     bool
		expectedStatus int
	}{
		{
			name:           "valid ID",
			pathValue:      "123",
			paramName:      "id",
			expectedID:     123,
			expectedOK:     true,
			expectedStatus: 200,
		},
		{
			name:           "zero ID",
			pathValue:      "0",
			paramName:      "id",
			expectedID:     0,
			expectedOK:     true,
			expectedStatus: 200,
		},
		{
			name:           "negative ID",
			pathValue:      "-1",
			paramName:      "id",
			expectedID:     -1,
			expectedOK:     true,
			expectedStatus: 200,
		},
		{
			name:           "invalid ID format",
			pathValue:      "abc",
			paramName:      "id",
			expectedID:     0,
			expectedOK:     false,
			expectedStatus: 400,
		},
		{
			name:           "empty ID",
			pathValue:      "",
			paramName:      "id",
			expectedID:     0,
			expectedOK:     false,
			expectedStatus: 400,
		},
		{
			name:           "ID too large",
			pathValue:      "999999999999",
			paramName:      "id",
			expectedID:     0,
			expectedOK:     false,
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := testutils.NewHTTPRequest("GET", "/test").
				WithPathValue(tt.paramName, tt.pathValue).
				Build()

			recorder := testutils.NewResponseRecorder()

			id, ok := ParseIDParam(recorder, req, tt.paramName)

			if ok != tt.expectedOK {
				t.Errorf("ParseIDParam() ok = %v, want %v", ok, tt.expectedOK)
			}

			if id != tt.expectedID {
				t.Errorf("ParseIDParam() id = %v, want %v", id, tt.expectedID)
			}

			if recorder.Code != tt.expectedStatus {
				t.Errorf("ParseIDParam() status = %v, want %v", recorder.Code, tt.expectedStatus)
			}
		})
	}
}

func TestParseRequestPagination(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		queryParams  map[string]string
		expectedPage int
		expectedSize int
	}{
		{
			name:         "default values",
			queryParams:  map[string]string{},
			expectedPage: 1,  // constants.DefaultPage
			expectedSize: 20, // constants.DefaultPageSize
		},
		{
			name: "valid pagination",
			queryParams: map[string]string{
				"page":      "2",
				"page_size": "50",
			},
			expectedPage: 2,
			expectedSize: 50,
		},
		{
			name: "invalid page",
			queryParams: map[string]string{
				"page":      "invalid",
				"page_size": "50",
			},
			expectedPage: 1, // default
			expectedSize: 50,
		},
		{
			name: "negative page",
			queryParams: map[string]string{
				"page":      "-1",
				"page_size": "50",
			},
			expectedPage: 1, // default
			expectedSize: 50,
		},
		{
			name: "zero page",
			queryParams: map[string]string{
				"page":      "0",
				"page_size": "50",
			},
			expectedPage: 1, // default
			expectedSize: 50,
		},
		{
			name: "invalid page_size",
			queryParams: map[string]string{
				"page":      "2",
				"page_size": "invalid",
			},
			expectedPage: 2,
			expectedSize: 20, // default
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Build URL with query parameters
			u, _ := url.Parse("/test")
			q := u.Query()
			for key, value := range tt.queryParams {
				q.Set(key, value)
			}
			u.RawQuery = q.Encode()

			req := testutils.NewHTTPRequest("GET", u.String()).Build()

			pagination := ParseRequestPagination(req)

			if pagination.Page != tt.expectedPage {
				t.Errorf("ParseRequestPagination() page = %v, want %v", pagination.Page, tt.expectedPage)
			}

			if pagination.PageSize != tt.expectedSize {
				t.Errorf("ParseRequestPagination() pageSize = %v, want %v", pagination.PageSize, tt.expectedSize)
			}
		})
	}
}

func TestParseQueryParam(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		queryParams  map[string]string
		paramName    string
		defaultValue string
		expected     string
	}{
		{
			name:         "existing parameter",
			queryParams:  map[string]string{"status": "active"},
			paramName:    "status",
			defaultValue: "all",
			expected:     "active",
		},
		{
			name:         "missing parameter",
			queryParams:  map[string]string{},
			paramName:    "status",
			defaultValue: "all",
			expected:     "all",
		},
		{
			name:         "empty parameter",
			queryParams:  map[string]string{"status": ""},
			paramName:    "status",
			defaultValue: "all",
			expected:     "all",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			u, _ := url.Parse("/test")
			q := u.Query()
			for key, value := range tt.queryParams {
				q.Set(key, value)
			}
			u.RawQuery = q.Encode()

			req := testutils.NewHTTPRequest("GET", u.String()).Build()

			result := ParseQueryParam(req, tt.paramName, tt.defaultValue)

			if result != tt.expected {
				t.Errorf("ParseQueryParam() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestValidateContentType(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		contentType    string
		expectedType   string
		expectedValid  bool
		expectedStatus int
	}{
		{
			name:           "valid content type",
			contentType:    "application/json",
			expectedType:   "application/json",
			expectedValid:  true,
			expectedStatus: 200,
		},
		{
			name:           "invalid content type",
			contentType:    "text/plain",
			expectedType:   "application/json",
			expectedValid:  false,
			expectedStatus: 400,
		},
		{
			name:           "missing content type",
			contentType:    "",
			expectedType:   "application/json",
			expectedValid:  false,
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := testutils.NewHTTPRequest("POST", "/test").
				WithHeader("Content-Type", tt.contentType).
				Build()

			recorder := testutils.NewResponseRecorder()

			valid := ValidateContentType(recorder, req, tt.expectedType)

			if valid != tt.expectedValid {
				t.Errorf("ValidateContentType() = %v, want %v", valid, tt.expectedValid)
			}

			if recorder.Code != tt.expectedStatus {
				t.Errorf("ValidateContentType() status = %v, want %v", recorder.Code, tt.expectedStatus)
			}
		})
	}
}

// Benchmark tests
func BenchmarkParseIDParam(b *testing.B) {
	req := testutils.NewHTTPRequest("GET", "/test").
		WithPathValue("id", "123").
		Build()

	recorder := testutils.NewResponseRecorder()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ParseIDParam(recorder, req, "id")
	}
}

func BenchmarkParseRequestPagination(b *testing.B) {
	req := testutils.NewHTTPRequest("GET", "/test?page=2&page_size=50").Build()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ParseRequestPagination(req)
	}
}

// Fuzz tests
func FuzzParseIDParam(f *testing.F) {
	f.Add("123")
	f.Add("0")
	f.Add("-1")
	f.Add("abc")
	f.Add("")
	f.Add("999999999999")

	f.Fuzz(func(t *testing.T, pathValue string) {
		req := testutils.NewHTTPRequest("GET", "/test").
			WithPathValue("id", pathValue).
			Build()

		recorder := testutils.NewResponseRecorder()

		id, ok := ParseIDParam(recorder, req, "id")

		// Property: if parsing succeeds, ID should be valid int32
		if ok && (id < -2147483648 || id > 2147483647) {
			t.Errorf("ParseIDParam returned invalid int32: %d", id)
		}
	})
}
