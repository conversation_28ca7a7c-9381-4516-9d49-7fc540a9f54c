// Package api provides HTTP request/response utilities, parameter parsing,
// and error handling functions to reduce code duplication across handlers
package api

import (
	"encoding/json"
	"errors"
	"net/http"
	"strconv"

	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/types"
)

// DecodeJSONRequest decodes JSON request body into the provided interface
// Returns true if successful, false if error (and sends error response)
func DecodeJSONRequest(w http.ResponseWriter, r *http.Request, v interface{}) bool {
	if err := json.NewDecoder(r.Body).Decode(v); err != nil {
		BadRequest(w, constants.ErrMsgInvalidJSON)
		return false
	}
	return true
}

// ParseIDParam parses an integer ID parameter from URL path
// Returns the parsed int32 and true if successful, 0 and false if error
func ParseIDParam(w http.ResponseWriter, r *http.Request, paramName string) (int32, bool) {
	paramStr := r.PathValue(paramName)
	if paramStr == "" {
		BadRequest(w, "missing "+paramName)
		return 0, false
	}

	paramInt, err := strconv.ParseInt(paramStr, 10, 32)
	if err != nil {
		BadRequest(w, "invalid "+paramName)
		return 0, false
	}

	return int32(paramInt), true
}

// ParseRequestPagination parses pagination parameters from query string
// Returns sanitized pagination params with defaults using standardized types
func ParseRequestPagination(r *http.Request) types.PaginationParams {
	page := constants.DefaultPage
	pageSize := constants.DefaultPageSize

	// Parse page
	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// Parse page_size
	if pageSizeStr := r.URL.Query().Get("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 {
			pageSize = ps
		}
	}

	return types.NewPaginationParams(page, pageSize)
}

// HandleCommonServiceError handles common service errors in a standardized way
// all handlers should use This function for consistent error handling
func HandleCommonServiceError(w http.ResponseWriter, err error, logger *logger.Logger, context string) {
	switch {
	case errors.Is(err, ErrNotFound):
		NotFound(w, constants.ErrMsgNotFound)
	case errors.Is(err, ErrUnauthorized):
		Unauthorized(w, constants.ErrMsgUnauthorized)
	case errors.Is(err, ErrForbidden):
		Forbidden(w, constants.ErrMsgForbidden)
	case errors.Is(err, ErrBadRequest):
		BadRequest(w, err.Error())
	case errors.Is(err, ErrDuplicateEntry):
		Error(w, 409, ErrCodeConflict, err.Error())
	case errors.Is(err, ErrInternal):
		logger.Error(context+" internal error", "error", err)
		InternalError(w, constants.ErrMsgInternalError)
	default:
		logger.Error(context+" unexpected error", "error", err)
		InternalError(w, constants.ErrMsgInternalError)
	}
}

// ParseQueryParam parses a string query parameter with a default value
func ParseQueryParam(r *http.Request, paramName string, defaultValue string) string {
	value := r.URL.Query().Get(paramName)
	if value == "" {
		return defaultValue
	}
	return value
}

// ParseQueryParamInt parses an integer query parameter with a default value
func ParseQueryParamInt(r *http.Request, paramName string, defaultValue int) int {
	valueStr := r.URL.Query().Get(paramName)
	if valueStr == "" {
		return defaultValue
	}

	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return defaultValue
	}

	return value
}

// ValidateContentType validates that the request has the expected content type
// Returns true if valid, false if not (and sends error response)
func ValidateContentType(w http.ResponseWriter, r *http.Request, expectedType string) bool {
	contentType := r.Header.Get("Content-Type")
	if contentType != expectedType {
		BadRequest(w, "Content-Type must be "+expectedType)
		return false
	}
	return true
}
