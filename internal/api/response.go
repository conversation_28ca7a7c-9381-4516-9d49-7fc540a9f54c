// Package api Package response 提供標準化的 HTTP 回應格式
// 統一所有 API 端點的回應結構和錯誤處理
package api

import (
	"encoding/json"
	"net/http"
)

// Response 標準 API 回應結構
// 統一所有 API 端點的回應格式
type Response struct {
	Success bool       `json:"success"`
	Data    any        `json:"data,omitempty"`
	Error   *ErrorInfo `json:"error,omitempty"`
	Meta    *Meta      `json:"meta,omitempty"`
}

// ErrorInfo 詳細錯誤資訊
// 提供結構化的錯誤回應
type ErrorInfo struct {
	Code    string         `json:"code"`
	Message string         `json:"message"`
	Details map[string]any `json:"details,omitempty"`
}

// Meta 分頁和中繼資料
// 用於支援分頁查詢的回應
type Meta struct {
	Page       int   `json:"page,omitempty"`
	PageSize   int   `json:"page_size,omitempty"`
	Total      int64 `json:"total,omitempty"`
	TotalPages int   `json:"total_pages,omitempty"`
}

// JSON 發送 JSON 格式的回應
// 統一的 JSON 回應處理，包含錯誤處理
func JSON(w http.ResponseWriter, statusCode int, data any) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		// 記錄內部錯誤但不暴露給客戶端
		http.Error(w, `{"success":false,"error":{"code":"INTERNAL_ERROR","message":"failed to encode response"}}`,
			http.StatusInternalServerError)
	}
}

// Success 發送成功回應
// 用於 API 操作成功時的標準回應
func Success(w http.ResponseWriter, data any) {
	JSON(w, http.StatusOK, Response{
		Success: true,
		Data:    data,
	})
}

// SuccessWithMeta 發送帶有中繼資料的成功回應
// 主要用於分頁查詢結果
func SuccessWithMeta(w http.ResponseWriter, data any, meta *Meta) {
	JSON(w, http.StatusOK, Response{
		Success: true,
		Data:    data,
		Meta:    meta,
	})
}

// Error 發送錯誤回應
// 標準化錯誤回應格式
func Error(w http.ResponseWriter, statusCode int, code, message string) {
	JSON(w, statusCode, Response{
		Success: false,
		Error: &ErrorInfo{
			Code:    code,
			Message: message,
		},
	})
}

// ErrorWithDetails 發送帶有詳細資訊的錯誤回應
// 用於驗證錯誤等需要提供詳細資訊的場景
func ErrorWithDetails(w http.ResponseWriter, statusCode int, code, message string, details map[string]any) {
	JSON(w, statusCode, Response{
		Success: false,
		Error: &ErrorInfo{
			Code:    code,
			Message: message,
			Details: details,
		},
	})
}

// BadRequest 發送 400 錯誤回應
func BadRequest(w http.ResponseWriter, message string) {
	Error(w, http.StatusBadRequest, ErrCodeBadRequest, message)
}

// Unauthorized 發送 401 錯誤回應
func Unauthorized(w http.ResponseWriter, message string) {
	Error(w, http.StatusUnauthorized, ErrCodeUnauthorized, message)
}

// Forbidden 發送 403 錯誤回應
func Forbidden(w http.ResponseWriter, message string) {
	Error(w, http.StatusForbidden, ErrCodeForbidden, message)
}

// NotFound 發送 404 錯誤回應
func NotFound(w http.ResponseWriter, message string) {
	Error(w, http.StatusNotFound, ErrCodeNotFound, message)
}

// InternalError 發送 500 錯誤回應
func InternalError(w http.ResponseWriter, message string) {
	Error(w, http.StatusInternalServerError, ErrCodeInternalError, message)
}

// ValidationError 發送驗證錯誤回應
func ValidationError(w http.ResponseWriter, message string, details map[string]any) {
	ErrorWithDetails(w, http.StatusBadRequest, ErrCodeValidation, message, details)
}
