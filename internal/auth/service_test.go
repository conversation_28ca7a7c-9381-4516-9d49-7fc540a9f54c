package auth

import (
	"context"
	"database/sql"
	"errors"
	"log/slog"
	"os"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// AuthQuerier defines the interface for database operations needed by auth service
type AuthQuerier interface {
	GetUserByEmail(ctx context.Context, email string) (sqlc.GetUserByEmailRow, error)
	GetUserByID(ctx context.Context, userID int32) (sqlc.GetUserByIDRow, error)
	UpdateUserLastLogin(ctx context.Context, userID int32) error
	CreateRegistrationRequest(ctx context.Context, params sqlc.CreateRegistrationRequestParams) (sqlc.RegistrationRequest, error)
}

// MockQueries implements the AuthQuerier interface for testing
type MockQueries struct {
	users              map[string]*sqlc.GetUserByEmailRow
	usersById          map[int32]*sqlc.GetUserByIDRow
	registrations      map[int32]*sqlc.RegistrationRequest
	lastRegistrationID int32
	lastLoginUpdates   []int32
	shouldFailGet      bool
	shouldFailCreate   bool
	shouldFailUpdate   bool
}

func NewMockQueries() *MockQueries {
	return &MockQueries{
		users:         make(map[string]*sqlc.GetUserByEmailRow),
		usersById:     make(map[int32]*sqlc.GetUserByIDRow),
		registrations: make(map[int32]*sqlc.RegistrationRequest),
	}
}

func (m *MockQueries) GetUserByEmail(ctx context.Context, email string) (sqlc.GetUserByEmailRow, error) {
	if m.shouldFailGet {
		return sqlc.GetUserByEmailRow{}, errors.New("database error")
	}

	user, exists := m.users[email]
	if !exists {
		return sqlc.GetUserByEmailRow{}, sql.ErrNoRows
	}
	return *user, nil
}

func (m *MockQueries) GetUserByID(ctx context.Context, userID int32) (sqlc.GetUserByIDRow, error) {
	if m.shouldFailGet {
		return sqlc.GetUserByIDRow{}, errors.New("database error")
	}

	user, exists := m.usersById[userID]
	if !exists {
		return sqlc.GetUserByIDRow{}, sql.ErrNoRows
	}
	return *user, nil
}

func (m *MockQueries) UpdateUserLastLogin(ctx context.Context, userID int32) error {
	if m.shouldFailUpdate {
		return errors.New("database error")
	}
	m.lastLoginUpdates = append(m.lastLoginUpdates, userID)
	return nil
}

func (m *MockQueries) CreateRegistrationRequest(ctx context.Context, params sqlc.CreateRegistrationRequestParams) (sqlc.RegistrationRequest, error) {
	if m.shouldFailCreate {
		return sqlc.RegistrationRequest{}, errors.New("database error")
	}

	m.lastRegistrationID++
	registration := sqlc.RegistrationRequest{
		ID:                m.lastRegistrationID,
		UnifiedBusinessNo: params.UnifiedBusinessNo,
		CompanyName:       params.CompanyName,
		CompanyType:       params.CompanyType,
		Address:           params.Address,
		CompanyOwner:      params.CompanyOwner,
		ContactPerson:     params.ContactPerson,
		JobTitle:          params.JobTitle,
		Phone:             params.Phone,
		Mobile:            params.Mobile,
		Email:             params.Email,
		BackupEmail:       params.BackupEmail,
		PasswordHash:      params.PasswordHash,
		Status:            params.Status,
		Remark:            params.Remark,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	m.registrations[registration.ID] = &registration
	return registration, nil
}

// MockTokenService for testing
type MockTokenService struct {
	shouldFailGenerate bool
	generatedToken     string
}

func NewMockTokenService() *MockTokenService {
	return &MockTokenService{
		generatedToken: "mock-jwt-token",
	}
}

func (m *MockTokenService) GenerateAccessToken(userID int32, email, username string, role sqlc.UserRole, companyID *int32) (string, error) {
	if m.shouldFailGenerate {
		return "", errors.New("token generation failed")
	}
	return m.generatedToken, nil
}

func (m *MockTokenService) GenerateRefreshToken(userID int32) (string, error) {
	if m.shouldFailGenerate {
		return "", errors.New("refresh token generation failed")
	}
	return "mock-refresh-token", nil
}

func (m *MockTokenService) ValidateToken(tokenString string) (*Claims, error) {
	if tokenString == m.generatedToken {
		return &Claims{
			UserID:   1,
			Email:    "<EMAIL>",
			Username: "testuser",
			Role:     sqlc.UserRoleSPO,
		}, nil
	}
	return nil, errors.New("invalid token")
}

func (m *MockTokenService) ValidateRefreshToken(tokenString string) (int32, error) {
	if tokenString == "mock-refresh-token" {
		return 1, nil
	}
	return 0, errors.New("invalid refresh token")
}

func TestService_Login(t *testing.T) {
	t.Parallel()

	now := time.Now()
	futureExpiry := now.Add(24 * time.Hour)
	pastExpiry := now.Add(-24 * time.Hour)

	tests := []struct {
		name           string
		request        LoginRequest
		setupMock      func(*MockQueries, *MockTokenService)
		expectedError  error
		expectedResult func(*LoginResponse) bool
	}{
		{
			name: "successful login",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0, // Active
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
					CompanyID:            sql.NullInt32{Valid: false},
					CompanyName:          sql.NullString{Valid: false},
				}
			},
			expectedError: nil,
			expectedResult: func(resp *LoginResponse) bool {
				return resp != nil &&
					resp.User.ID == 1 &&
					resp.User.Email == "<EMAIL>" &&
					resp.User.Role == sqlc.UserRoleSPO &&
					resp.Token == "mock-jwt-token"
			},
		},
		{
			name: "user not found",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "password",
			},
			setupMock:     func(mq *MockQueries, mts *MockTokenService) {},
			expectedError: ErrInvalidCredentials,
		},
		{
			name: "invalid password",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "WrongPassword",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("CorrectPassword")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0,
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
				}
			},
			expectedError: ErrInvalidCredentials,
		},
		{
			name: "password expired",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0,
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: pastExpiry,
					LastLoginAt:          now,
				}
			},
			expectedError: ErrPasswordExpired,
		},
		{
			name: "account deleted",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue3, // Deleted
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
				}
			},
			expectedError: ErrAccountDeleted,
		},
		{
			name: "account pending changes",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue1, // Pending changes
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
				}
			},
			expectedError: ErrAccountPendingChanges,
		},
		{
			name: "database error on user lookup",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				mq.shouldFailGet = true
			},
			expectedError: ErrInternalError,
		},
		{
			name: "token generation failure",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0,
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
				}
				mts.shouldFailGenerate = true
			},
			expectedError: ErrInternalError,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockQueries := NewMockQueries()
			mockTokenService := NewMockTokenService()
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			service := &Service{
				queries:         mockQueries,
				tokenService:    mockTokenService,
				passwordService: NewPasswordService(),
				logger:          logger,
			}

			tt.setupMock(mockQueries, mockTokenService)

			// Execute
			ctx := testutils.CreateTestContext()
			result, err := service.Login(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				if err == nil {
					t.Errorf("expected error %v, but got nil", tt.expectedError)
					return
				}
				if !errors.Is(err, tt.expectedError) && err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, but got %v", tt.expectedError, err)
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if tt.expectedResult != nil && !tt.expectedResult(result) {
				t.Errorf("result validation failed for test case: %s", tt.name)
			}
		})
	}
}

func TestService_GetUserByID(t *testing.T) {
	t.Parallel()

	now := time.Now()

	tests := []struct {
		name          string
		userID        int32
		setupMock     func(*MockQueries)
		expectedError error
		expectedUser  *UserInfo
	}{
		{
			name:   "successful user retrieval",
			userID: 1,
			setupMock: func(mq *MockQueries) {
				mq.usersById[1] = &sqlc.GetUserByIDRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0,
					LastLoginAt:          now,
					PasswordExpirationAt: now.Add(24 * time.Hour),
					CompanyID:            sql.NullInt32{Valid: false},
					CompanyName:          sql.NullString{Valid: false},
				}
			},
			expectedError: nil,
			expectedUser: &UserInfo{
				ID:                1,
				Username:          "admin",
				Email:             "<EMAIL>",
				Role:              sqlc.UserRoleSPO,
				Status:            sqlc.UserStatusValue0,
				CompanyID:         nil,
				CompanyName:       nil,
				LastLoginAt:       now,
				PasswordExpiredAt: now.Add(24 * time.Hour),
			},
		},
		{
			name:   "user not found",
			userID: 999,
			setupMock: func(mq *MockQueries) {
				// No user setup - should return not found
			},
			expectedError: ErrUserNotFound,
		},
		{
			name:   "database error",
			userID: 1,
			setupMock: func(mq *MockQueries) {
				mq.shouldFailGet = true
			},
			expectedError: ErrInternalError,
		},
		{
			name:   "user with company information",
			userID: 2,
			setupMock: func(mq *MockQueries) {
				mq.usersById[2] = &sqlc.GetUserByIDRow{
					ID:                   2,
					Username:             "company_user",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleCompany,
					Status:               sqlc.UserStatusValue0,
					LastLoginAt:          now,
					PasswordExpirationAt: now.Add(24 * time.Hour),
					CompanyID:            sql.NullInt32{Int32: 10, Valid: true},
					CompanyName:          sql.NullString{String: "Test Company", Valid: true},
				}
			},
			expectedError: nil,
			expectedUser: &UserInfo{
				ID:                2,
				Username:          "company_user",
				Email:             "<EMAIL>",
				Role:              sqlc.UserRoleCompany,
				Status:            sqlc.UserStatusValue0,
				CompanyID:         testutils.Int32Ptr(10),
				CompanyName:       testutils.StringPtr("Test Company"),
				LastLoginAt:       now,
				PasswordExpiredAt: now.Add(24 * time.Hour),
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockQueries := NewMockQueries()
			mockTokenService := NewMockTokenService()
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			service := &Service{
				queries:         mockQueries,
				tokenService:    mockTokenService,
				passwordService: NewPasswordService(),
				logger:          logger,
			}

			tt.setupMock(mockQueries)

			// Execute
			ctx := testutils.CreateTestContext()
			result, err := service.GetUserByID(ctx, tt.userID)

			// Assert
			if tt.expectedError != nil {
				if err == nil {
					t.Errorf("expected error %v, but got nil", tt.expectedError)
					return
				}
				if !errors.Is(err, tt.expectedError) && err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, but got %v", tt.expectedError, err)
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if tt.expectedUser != nil {
				testutils.CompareJSON(t, tt.expectedUser, result)
			}
		})
	}
}

func TestService_Register(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		request        RegistrationRequest
		setupMock      func(*MockQueries)
		expectedError  error
		validateResult func(*RegistrationResponse) bool
	}{
		{
			name: "successful registration",
			request: RegistrationRequest{
				UnifiedBusinessNo: "12345678",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			setupMock: func(mq *MockQueries) {
				// No special setup needed for successful case
			},
			expectedError: nil,
			validateResult: func(resp *RegistrationResponse) bool {
				return resp != nil &&
					resp.ID > 0 &&
					resp.Status == constants.RegistrationStatuses.PendingRegistration &&
					resp.Message == constants.MsgRegistered
			},
		},
		{
			name: "registration with optional fields",
			request: RegistrationRequest{
				UnifiedBusinessNo: "87654321",
				CompanyName:       "Another Company",
				CompanyType:       "資訊服務廠商",
				Address:           testutils.StringPtr("123 Test Street"),
				CompanyOwner:      "Alice Smith",
				ContactPerson:     "Bob Smith",
				JobTitle:          testutils.StringPtr("Manager"),
				Phone:             testutils.StringPtr("02-12345678"),
				Mobile:            testutils.StringPtr("0912-345678"),
				Email:             "<EMAIL>",
				BackupEmail:       testutils.StringPtr("<EMAIL>"),
				Password:          "AnotherPass123!",
				Remark:            testutils.StringPtr("Test registration"),
			},
			setupMock: func(mq *MockQueries) {
				// No special setup needed
			},
			expectedError: nil,
			validateResult: func(resp *RegistrationResponse) bool {
				return resp != nil &&
					resp.ID > 0 &&
					resp.Status == constants.RegistrationStatuses.PendingRegistration
			},
		},
		{
			name: "database error during registration",
			request: RegistrationRequest{
				UnifiedBusinessNo: "12345678",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			setupMock: func(mq *MockQueries) {
				mq.shouldFailCreate = true
			},
			expectedError: ErrInternalError,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockQueries := NewMockQueries()
			mockTokenService := NewMockTokenService()
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			service := &Service{
				queries:         mockQueries,
				tokenService:    mockTokenService,
				passwordService: NewPasswordService(),
				logger:          logger,
			}

			tt.setupMock(mockQueries)

			// Execute
			ctx := testutils.CreateTestContext()
			result, err := service.Register(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				if err == nil {
					t.Errorf("expected error %v, but got nil", tt.expectedError)
					return
				}
				if !errors.Is(err, tt.expectedError) && err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, but got %v", tt.expectedError, err)
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if tt.validateResult != nil && !tt.validateResult(result) {
				t.Errorf("result validation failed for test case: %s", tt.name)
			}
		})
	}
}

func TestService_validateUserStatus(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(nil, nil, logger)

	tests := []struct {
		name          string
		status        sqlc.UserStatus
		expectedError error
	}{
		{
			name:          "active user",
			status:        sqlc.UserStatusValue0,
			expectedError: nil,
		},
		{
			name:          "pending changes",
			status:        sqlc.UserStatusValue1,
			expectedError: ErrAccountPendingChanges,
		},
		{
			name:          "changes rejected",
			status:        sqlc.UserStatusValue2,
			expectedError: ErrAccountChangesRejected,
		},
		{
			name:          "deleted account",
			status:        sqlc.UserStatusValue3,
			expectedError: ErrAccountDeleted,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := service.validateUserStatus(tt.status)

			if tt.expectedError == nil {
				if err != nil {
					t.Errorf("expected no error, but got %v", err)
				}
			} else {
				if err == nil {
					t.Errorf("expected error %v, but got nil", tt.expectedError)
				} else if !errors.Is(err, tt.expectedError) && err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, but got %v", tt.expectedError, err)
				}
			}
		})
	}
}

// Benchmark tests
func BenchmarkService_Login(b *testing.B) {
	mockQueries := NewMockQueries()
	mockTokenService := NewMockTokenService()
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := &Service{
		queries:         mockQueries,
		tokenService:    mockTokenService,
		passwordService: NewPasswordService(),
		logger:          logger,
	}

	// Setup test user
	hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
	mockQueries.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
		ID:                   1,
		Username:             "admin",
		Email:                "<EMAIL>",
		UserRole:             sqlc.UserRoleSPO,
		Status:               sqlc.UserStatusValue0,
		PasswordHash:         hashedPassword,
		PasswordExpirationAt: time.Now().Add(24 * time.Hour),
		LastLoginAt:          time.Now(),
	}

	req := LoginRequest{
		Email:    "<EMAIL>",
		Password: "AdminPass123!",
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.Login(ctx, req)
		if err != nil {
			b.Errorf("unexpected error: %v", err)
		}
	}
}

func BenchmarkService_Register(b *testing.B) {
	mockQueries := NewMockQueries()
	mockTokenService := NewMockTokenService()
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := &Service{
		queries:         mockQueries,
		tokenService:    mockTokenService,
		passwordService: NewPasswordService(),
		logger:          logger,
	}

	req := RegistrationRequest{
		UnifiedBusinessNo: "12345678",
		CompanyName:       "Test Company",
		CompanyType:       "軟體廠商",
		CompanyOwner:      "John Doe",
		ContactPerson:     "Jane Doe",
		Email:             "<EMAIL>",
		Password:          "SecurePass123!",
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.Register(ctx, req)
		if err != nil {
			b.Errorf("unexpected error: %v", err)
		}
	}
}
