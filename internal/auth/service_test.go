package auth

import (
	"context"
	"database/sql"
	"errors"
	"log/slog"
	"os"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// These interfaces define only the methods that the tests need to mock

// QueriesInterface defines the database operations needed by the auth service
type QueriesInterface interface {
	GetUserByEmail(ctx context.Context, email string) (sqlc.GetUserByEmailRow, error)
	GetUserByID(ctx context.Context, id int32) (sqlc.GetUserByIDRow, error)
	UpdateUserLastLogin(ctx context.Context, id int32) error
	CreateRegistrationRequest(ctx context.Context, arg sqlc.CreateRegistrationRequestParams) (sqlc.RegistrationRequest, error)
}

// TokenServiceInterface defines the token operations needed by the auth service
type TokenServiceInterface interface {
	GenerateAccessToken(userID int32, email, username string, role sqlc.UserRole, companyID *int32) (string, error)
	GenerateRefreshToken(userID int32) (string, error)
	ValidateToken(tokenString string) (*Claims, error)
	ValidateRefreshToken(tokenString string) (int32, error)
}

// TestService wraps the auth service with interfaces for testing
type TestService struct {
	queries         QueriesInterface
	tokenService    TokenServiceInterface
	passwordService *PasswordService
	logger          *logger.Logger
}

// NewTestService creates a new auth service for testing
func NewTestService(queries QueriesInterface, tokenService TokenServiceInterface, logger *logger.Logger) *TestService {
	return &TestService{
		queries:         queries,
		tokenService:    tokenService,
		passwordService: NewPasswordService(),
		logger:          logger,
	}
}

// Login authenticates a user and returns login information (test version)
func (s *TestService) Login(ctx context.Context, req LoginRequest) (*LoginResponse, error) {
	// Get user by email
	user, err := s.queries.GetUserByEmail(ctx, req.Email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrInvalidCredentials
		}
		s.logger.Error("failed to get user by email", "error", err, "email", req.Email)
		return nil, ErrInternalError
	}

	// Check user status
	if err := s.validateUserStatus(user.Status); err != nil {
		return nil, err
	}

	// Verify password
	if err := s.passwordService.VerifyPassword(req.Password, user.PasswordHash); err != nil {
		return nil, ErrInvalidCredentials
	}

	// Check password expiration
	if user.PasswordExpirationAt.Before(time.Now()) {
		return nil, ErrPasswordExpired
	}

	// Generate access token
	var companyID *int32
	if user.CompanyID.Valid {
		companyID = &user.CompanyID.Int32
	}

	token, err := s.tokenService.GenerateAccessToken(
		user.ID,
		user.Email,
		user.Username,
		user.UserRole,
		companyID,
	)
	if err != nil {
		s.logger.Error("failed to generate access token", "error", err, "user_id", user.ID)
		return nil, ErrInternalError
	}

	// Update last login time
	if err := s.queries.UpdateUserLastLogin(ctx, user.ID); err != nil {
		s.logger.Error("failed to update last login", "error", err, "user_id", user.ID)
		// Continue even if this fails
	}

	// Prepare response
	var companyName *string
	if user.CompanyName.Valid {
		companyName = &user.CompanyName.String
	}

	return &LoginResponse{
		User: &UserInfo{
			ID:                user.ID,
			Username:          user.Username,
			Email:             user.Email,
			Role:              user.UserRole,
			Status:            user.Status,
			CompanyID:         companyID,
			CompanyName:       companyName,
			LastLoginAt:       user.LastLoginAt,
			PasswordExpiredAt: user.PasswordExpirationAt,
		},
		Token: token,
	}, nil
}

// GetUserByID retrieves user information by ID (test version)
func (s *TestService) GetUserByID(ctx context.Context, userID int32) (*UserInfo, error) {
	user, err := s.queries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		s.logger.Error("failed to get user by ID", "error", err, "user_id", userID)
		return nil, ErrInternalError
	}

	return s.mapUserToUserInfo(&user), nil
}

// Register creates a new user registration request (test version)
func (s *TestService) Register(ctx context.Context, req RegistrationRequest) (*RegistrationResponse, error) {
	// Hash password
	hashedPassword, err := s.passwordService.HashPassword(req.Password)
	if err != nil {
		s.logger.Error("failed to hash password during registration", "error", err)
		return nil, ErrInternalError
	}

	// Convert optional fields to sql.Null types
	var address sql.NullString
	if req.Address != nil {
		address.String = *req.Address
		address.Valid = true
	}

	var jobTitle sql.NullString
	if req.JobTitle != nil {
		jobTitle.String = *req.JobTitle
		jobTitle.Valid = true
	}

	var phone sql.NullString
	if req.Phone != nil {
		phone.String = *req.Phone
		phone.Valid = true
	}

	var mobile sql.NullString
	if req.Mobile != nil {
		mobile.String = *req.Mobile
		mobile.Valid = true
	}

	var backupEmail sql.NullString
	if req.BackupEmail != nil {
		backupEmail.String = *req.BackupEmail
		backupEmail.Valid = true
	}

	var remark sql.NullString
	if req.Remark != nil {
		remark.String = *req.Remark
		remark.Valid = true
	}

	// Create company type
	var companyType sqlc.NullCompanyType
	if req.CompanyType != "" {
		companyType.CompanyType = sqlc.CompanyType(req.CompanyType)
		companyType.Valid = true
	}

	// Create registration request
	registration, err := s.queries.CreateRegistrationRequest(ctx, sqlc.CreateRegistrationRequestParams{
		UnifiedBusinessNo: req.UnifiedBusinessNo,
		CompanyName:       req.CompanyName,
		CompanyType:       companyType,
		Address:           address,
		CompanyOwner:      req.CompanyOwner,
		ContactPerson:     req.ContactPerson,
		JobTitle:          jobTitle,
		Phone:             phone,
		Mobile:            mobile,
		Email:             req.Email,
		BackupEmail:       backupEmail,
		PasswordHash:      hashedPassword,
		Status:            constants.RegistrationStatuses.PendingRegistration,
		Remark:            remark,
	})

	if err != nil {
		s.logger.Error("failed to create registration request", "error", err, "email", req.Email)
		return nil, ErrInternalError
	}

	return &RegistrationResponse{
		ID:      registration.ID,
		Status:  registration.Status,
		Message: constants.MsgRegistered,
	}, nil
}

// validateUserStatus checks if user status allows login (test version)
func (s *TestService) validateUserStatus(status sqlc.UserStatus) error {
	switch status {
	case sqlc.UserStatusValue0:
		return nil
	case sqlc.UserStatusValue1:
		return ErrAccountPendingChanges
	case sqlc.UserStatusValue2:
		return ErrAccountChangesRejected
	case sqlc.UserStatusValue3:
		return ErrAccountDeleted
	default:
		return ErrAccountPending
	}
}

// mapUserToUserInfo converts database user to UserInfo (test version)
func (s *TestService) mapUserToUserInfo(user *sqlc.GetUserByIDRow) *UserInfo {
	var companyID *int32
	if user.CompanyID.Valid {
		companyID = &user.CompanyID.Int32
	}
	var companyName *string
	if user.CompanyName.Valid {
		companyName = &user.CompanyName.String
	}

	return &UserInfo{
		ID:                user.ID,
		Username:          user.Username,
		Email:             user.Email,
		Role:              user.UserRole,
		Status:            user.Status,
		CompanyID:         companyID,
		CompanyName:       companyName,
		LastLoginAt:       user.LastLoginAt,
		PasswordExpiredAt: user.PasswordExpirationAt,
	}
}

// MockQueries implements the AuthQuerier interface for testing
type MockQueries struct {
	users              map[string]*sqlc.GetUserByEmailRow
	usersById          map[int32]*sqlc.GetUserByIDRow
	registrations      map[int32]*sqlc.RegistrationRequest
	lastRegistrationID int32
	lastLoginUpdates   []int32
	shouldFailGet      bool
	shouldFailCreate   bool
	shouldFailUpdate   bool
}

func NewMockQueries() *MockQueries {
	return &MockQueries{
		users:         make(map[string]*sqlc.GetUserByEmailRow),
		usersById:     make(map[int32]*sqlc.GetUserByIDRow),
		registrations: make(map[int32]*sqlc.RegistrationRequest),
	}
}

func (m *MockQueries) GetUserByEmail(ctx context.Context, email string) (sqlc.GetUserByEmailRow, error) {
	if m.shouldFailGet {
		return sqlc.GetUserByEmailRow{}, errors.New("database error")
	}

	user, exists := m.users[email]
	if !exists {
		return sqlc.GetUserByEmailRow{}, sql.ErrNoRows
	}
	return *user, nil
}

func (m *MockQueries) GetUserByID(ctx context.Context, userID int32) (sqlc.GetUserByIDRow, error) {
	if m.shouldFailGet {
		return sqlc.GetUserByIDRow{}, errors.New("database error")
	}

	user, exists := m.usersById[userID]
	if !exists {
		return sqlc.GetUserByIDRow{}, sql.ErrNoRows
	}
	return *user, nil
}

func (m *MockQueries) UpdateUserLastLogin(ctx context.Context, userID int32) error {
	if m.shouldFailUpdate {
		return errors.New("database error")
	}
	m.lastLoginUpdates = append(m.lastLoginUpdates, userID)
	return nil
}

func (m *MockQueries) CreateRegistrationRequest(ctx context.Context, params sqlc.CreateRegistrationRequestParams) (sqlc.RegistrationRequest, error) {
	if m.shouldFailCreate {
		return sqlc.RegistrationRequest{}, errors.New("database error")
	}

	m.lastRegistrationID++
	registration := sqlc.RegistrationRequest{
		ID:                m.lastRegistrationID,
		UnifiedBusinessNo: params.UnifiedBusinessNo,
		CompanyName:       params.CompanyName,
		CompanyType:       params.CompanyType,
		Address:           params.Address,
		CompanyOwner:      params.CompanyOwner,
		ContactPerson:     params.ContactPerson,
		JobTitle:          params.JobTitle,
		Phone:             params.Phone,
		Mobile:            params.Mobile,
		Email:             params.Email,
		BackupEmail:       params.BackupEmail,
		PasswordHash:      params.PasswordHash,
		Status:            params.Status,
		Remark:            params.Remark,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	m.registrations[registration.ID] = &registration
	return registration, nil
}

// MockTokenService for testing
type MockTokenService struct {
	shouldFailGenerate bool
	generatedToken     string
}

func NewMockTokenService() *MockTokenService {
	return &MockTokenService{
		generatedToken: "mock-jwt-token",
	}
}

func (m *MockTokenService) GenerateAccessToken(userID int32, email, username string, role sqlc.UserRole, companyID *int32) (string, error) {
	if m.shouldFailGenerate {
		return "", errors.New("token generation failed")
	}
	return m.generatedToken, nil
}

func (m *MockTokenService) GenerateRefreshToken(userID int32) (string, error) {
	if m.shouldFailGenerate {
		return "", errors.New("refresh token generation failed")
	}
	return "mock-refresh-token", nil
}

func (m *MockTokenService) ValidateToken(tokenString string) (*Claims, error) {
	if tokenString == m.generatedToken {
		return &Claims{
			UserID:   1,
			Email:    "<EMAIL>",
			Username: "testuser",
			Role:     sqlc.UserRoleSPO,
		}, nil
	}
	return nil, errors.New("invalid token")
}

func (m *MockTokenService) ValidateRefreshToken(tokenString string) (int32, error) {
	if tokenString == "mock-refresh-token" {
		return 1, nil
	}
	return 0, errors.New("invalid refresh token")
}

func TestService_Login(t *testing.T) {
	t.Parallel()

	now := time.Now()
	futureExpiry := now.Add(24 * time.Hour)
	pastExpiry := now.Add(-24 * time.Hour)

	tests := []struct {
		name           string
		request        LoginRequest
		setupMock      func(*MockQueries, *MockTokenService)
		expectedError  error
		expectedResult func(*LoginResponse) bool
	}{
		{
			name: "successful login",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0, // Active
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
					CompanyID:            sql.NullInt32{Valid: false},
					CompanyName:          sql.NullString{Valid: false},
				}
			},
			expectedError: nil,
			expectedResult: func(resp *LoginResponse) bool {
				return resp != nil &&
					resp.User.ID == 1 &&
					resp.User.Email == "<EMAIL>" &&
					resp.User.Role == sqlc.UserRoleSPO &&
					resp.Token == "mock-jwt-token"
			},
		},
		{
			name: "user not found",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "password",
			},
			setupMock:     func(mq *MockQueries, mts *MockTokenService) {},
			expectedError: ErrInvalidCredentials,
		},
		{
			name: "invalid password",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "WrongPassword",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("CorrectPassword")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0,
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
				}
			},
			expectedError: ErrInvalidCredentials,
		},
		{
			name: "password expired",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0,
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: pastExpiry,
					LastLoginAt:          now,
				}
			},
			expectedError: ErrPasswordExpired,
		},
		{
			name: "account deleted",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue3, // Deleted
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
				}
			},
			expectedError: ErrAccountDeleted,
		},
		{
			name: "account pending changes",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue1, // Pending changes
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
				}
			},
			expectedError: ErrAccountPendingChanges,
		},
		{
			name: "database error on user lookup",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				mq.shouldFailGet = true
			},
			expectedError: ErrInternalError,
		},
		{
			name: "token generation failure",
			request: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mq *MockQueries, mts *MockTokenService) {
				hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
				mq.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0,
					PasswordHash:         hashedPassword,
					PasswordExpirationAt: futureExpiry,
					LastLoginAt:          now,
				}
				mts.shouldFailGenerate = true
			},
			expectedError: ErrInternalError,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockQueries := NewMockQueries()
			mockTokenService := NewMockTokenService()
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			service := NewTestService(mockQueries, mockTokenService, logger)

			tt.setupMock(mockQueries, mockTokenService)

			// Execute
			ctx := testutils.CreateTestContext()
			result, err := service.Login(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				if err == nil {
					t.Errorf("expected error %v, but got nil", tt.expectedError)
					return
				}
				if !errors.Is(err, tt.expectedError) && err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, but got %v", tt.expectedError, err)
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if tt.expectedResult != nil && !tt.expectedResult(result) {
				t.Errorf("result validation failed for test case: %s", tt.name)
			}
		})
	}
}

func TestService_GetUserByID(t *testing.T) {
	t.Parallel()

	now := time.Now()

	tests := []struct {
		name          string
		userID        int32
		setupMock     func(*MockQueries)
		expectedError error
		expectedUser  *UserInfo
	}{
		{
			name:   "successful user retrieval",
			userID: 1,
			setupMock: func(mq *MockQueries) {
				mq.usersById[1] = &sqlc.GetUserByIDRow{
					ID:                   1,
					Username:             "admin",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleSPO,
					Status:               sqlc.UserStatusValue0,
					LastLoginAt:          now,
					PasswordExpirationAt: now.Add(24 * time.Hour),
					CompanyID:            sql.NullInt32{Valid: false},
					CompanyName:          sql.NullString{Valid: false},
				}
			},
			expectedError: nil,
			expectedUser: &UserInfo{
				ID:                1,
				Username:          "admin",
				Email:             "<EMAIL>",
				Role:              sqlc.UserRoleSPO,
				Status:            sqlc.UserStatusValue0,
				CompanyID:         nil,
				CompanyName:       nil,
				LastLoginAt:       now,
				PasswordExpiredAt: now.Add(24 * time.Hour),
			},
		},
		{
			name:   "user not found",
			userID: 999,
			setupMock: func(mq *MockQueries) {
				// No user setup - should return not found
			},
			expectedError: ErrUserNotFound,
		},
		{
			name:   "database error",
			userID: 1,
			setupMock: func(mq *MockQueries) {
				mq.shouldFailGet = true
			},
			expectedError: ErrInternalError,
		},
		{
			name:   "user with company information",
			userID: 2,
			setupMock: func(mq *MockQueries) {
				mq.usersById[2] = &sqlc.GetUserByIDRow{
					ID:                   2,
					Username:             "company_user",
					Email:                "<EMAIL>",
					UserRole:             sqlc.UserRoleCompany,
					Status:               sqlc.UserStatusValue0,
					LastLoginAt:          now,
					PasswordExpirationAt: now.Add(24 * time.Hour),
					CompanyID:            sql.NullInt32{Int32: 10, Valid: true},
					CompanyName:          sql.NullString{String: "Test Company", Valid: true},
				}
			},
			expectedError: nil,
			expectedUser: &UserInfo{
				ID:                2,
				Username:          "company_user",
				Email:             "<EMAIL>",
				Role:              sqlc.UserRoleCompany,
				Status:            sqlc.UserStatusValue0,
				CompanyID:         testutils.Int32Ptr(10),
				CompanyName:       testutils.StringPtr("Test Company"),
				LastLoginAt:       now,
				PasswordExpiredAt: now.Add(24 * time.Hour),
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockQueries := NewMockQueries()
			mockTokenService := NewMockTokenService()
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			service := NewTestService(mockQueries, mockTokenService, logger)

			tt.setupMock(mockQueries)

			// Execute
			ctx := testutils.CreateTestContext()
			result, err := service.GetUserByID(ctx, tt.userID)

			// Assert
			if tt.expectedError != nil {
				if err == nil {
					t.Errorf("expected error %v, but got nil", tt.expectedError)
					return
				}
				if !errors.Is(err, tt.expectedError) && err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, but got %v", tt.expectedError, err)
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if tt.expectedUser != nil {
				testutils.CompareJSON(t, tt.expectedUser, result)
			}
		})
	}
}

func TestService_Register(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		request        RegistrationRequest
		setupMock      func(*MockQueries)
		expectedError  error
		validateResult func(*RegistrationResponse) bool
	}{
		{
			name: "successful registration",
			request: RegistrationRequest{
				UnifiedBusinessNo: "12345678",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			setupMock: func(mq *MockQueries) {
				// No special setup needed for successful case
			},
			expectedError: nil,
			validateResult: func(resp *RegistrationResponse) bool {
				return resp != nil &&
					resp.ID > 0 &&
					resp.Status == constants.RegistrationStatuses.PendingRegistration &&
					resp.Message == constants.MsgRegistered
			},
		},
		{
			name: "registration with optional fields",
			request: RegistrationRequest{
				UnifiedBusinessNo: "87654321",
				CompanyName:       "Another Company",
				CompanyType:       "資訊服務廠商",
				Address:           testutils.StringPtr("123 Test Street"),
				CompanyOwner:      "Alice Smith",
				ContactPerson:     "Bob Smith",
				JobTitle:          testutils.StringPtr("Manager"),
				Phone:             testutils.StringPtr("02-12345678"),
				Mobile:            testutils.StringPtr("0912-345678"),
				Email:             "<EMAIL>",
				BackupEmail:       testutils.StringPtr("<EMAIL>"),
				Password:          "AnotherPass123!",
				Remark:            testutils.StringPtr("Test registration"),
			},
			setupMock: func(mq *MockQueries) {
				// No special setup needed
			},
			expectedError: nil,
			validateResult: func(resp *RegistrationResponse) bool {
				return resp != nil &&
					resp.ID > 0 &&
					resp.Status == constants.RegistrationStatuses.PendingRegistration
			},
		},
		{
			name: "database error during registration",
			request: RegistrationRequest{
				UnifiedBusinessNo: "12345678",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			setupMock: func(mq *MockQueries) {
				mq.shouldFailCreate = true
			},
			expectedError: ErrInternalError,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockQueries := NewMockQueries()
			mockTokenService := NewMockTokenService()
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			service := NewTestService(mockQueries, mockTokenService, logger)

			tt.setupMock(mockQueries)

			// Execute
			ctx := testutils.CreateTestContext()
			result, err := service.Register(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				if err == nil {
					t.Errorf("expected error %v, but got nil", tt.expectedError)
					return
				}
				if !errors.Is(err, tt.expectedError) && err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, but got %v", tt.expectedError, err)
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if tt.validateResult != nil && !tt.validateResult(result) {
				t.Errorf("result validation failed for test case: %s", tt.name)
			}
		})
	}
}

func TestService_validateUserStatus(t *testing.T) {
	t.Parallel()

	// Following Architecture.md: test private methods through public interfaces
	// This test validates user status through the Login method

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	mockQueries := NewMockQueries()
	mockTokenService := NewMockTokenService()
	service := NewTestService(mockQueries, mockTokenService, logger)

	tests := []struct {
		name          string
		status        sqlc.UserStatus
		expectedError error
	}{
		{
			name:          "active user",
			status:        sqlc.UserStatusValue0,
			expectedError: nil,
		},
		{
			name:          "pending changes",
			status:        sqlc.UserStatusValue1,
			expectedError: ErrAccountPendingChanges,
		},
		{
			name:          "changes rejected",
			status:        sqlc.UserStatusValue2,
			expectedError: ErrAccountChangesRejected,
		},
		{
			name:          "deleted account",
			status:        sqlc.UserStatusValue3,
			expectedError: ErrAccountDeleted,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup user with specific status
			hashedPassword, _ := NewPasswordService().HashPassword("TestPass123!")
			mockQueries.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
				ID:                   1,
				Username:             "testuser",
				Email:                "<EMAIL>",
				UserRole:             sqlc.UserRoleSPO,
				Status:               tt.status,
				PasswordHash:         hashedPassword,
				PasswordExpirationAt: time.Now().Add(24 * time.Hour),
				LastLoginAt:          time.Now(),
			}

			// Test through Login method
			ctx := testutils.CreateTestContext()
			_, err := service.Login(ctx, LoginRequest{
				Email:    "<EMAIL>",
				Password: "TestPass123!",
			})

			if tt.expectedError == nil {
				if err != nil {
					t.Errorf("expected no error, but got %v", err)
				}
			} else {
				if err == nil {
					t.Errorf("expected error %v, but got nil", tt.expectedError)
				} else if !errors.Is(err, tt.expectedError) && err.Error() != tt.expectedError.Error() {
					t.Errorf("expected error %v, but got %v", tt.expectedError, err)
				}
			}

			// Clean up for next test
			delete(mockQueries.users, "<EMAIL>")
		})
	}
}

// Benchmark tests
func BenchmarkService_Login(b *testing.B) {
	mockQueries := NewMockQueries()
	mockTokenService := NewMockTokenService()
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewTestService(mockQueries, mockTokenService, logger)

	// Setup test user
	hashedPassword, _ := NewPasswordService().HashPassword("AdminPass123!")
	mockQueries.users["<EMAIL>"] = &sqlc.GetUserByEmailRow{
		ID:                   1,
		Username:             "admin",
		Email:                "<EMAIL>",
		UserRole:             sqlc.UserRoleSPO,
		Status:               sqlc.UserStatusValue0,
		PasswordHash:         hashedPassword,
		PasswordExpirationAt: time.Now().Add(24 * time.Hour),
		LastLoginAt:          time.Now(),
	}

	req := LoginRequest{
		Email:    "<EMAIL>",
		Password: "AdminPass123!",
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.Login(ctx, req)
		if err != nil {
			b.Errorf("unexpected error: %v", err)
		}
	}
}

func BenchmarkService_Register(b *testing.B) {
	mockQueries := NewMockQueries()
	mockTokenService := NewMockTokenService()
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewTestService(mockQueries, mockTokenService, logger)

	req := RegistrationRequest{
		UnifiedBusinessNo: "12345678",
		CompanyName:       "Test Company",
		CompanyType:       "軟體廠商",
		CompanyOwner:      "John Doe",
		ContactPerson:     "Jane Doe",
		Email:             "<EMAIL>",
		Password:          "SecurePass123!",
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.Register(ctx, req)
		if err != nil {
			b.Errorf("unexpected error: %v", err)
		}
	}
}
