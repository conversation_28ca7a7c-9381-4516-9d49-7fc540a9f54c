// Package database 提供資料庫連線管理功能
// 使用 pgx v5 原生驅動程式與 PostgreSQL 通訊
// 支援連線池、健康檢查、交易管理和類型安全查詢
package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/jackc/pgx/v5/stdlib" // pgx driver for database/sql
	"github.com/koopa0/pms-api-v2/internal/config"
)

// DB 封裝資料庫連線
// 提供類型安全的資料庫操作和連線池管理
type DB struct {
	db *sql.DB
}

// New 建立新的資料庫連線池
// 使用 pgx v5 原生驅動程式和連線池管理
// 返回已測試可用的資料庫連線池
func New(cfg *config.DatabaseConfig) (*DB, error) {
	// 使用 pgx 驅動程式開啟資料庫連線
	db, err := sql.Open("pgx", cfg.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 設定連線池參數（遵循 Architecture.md 中的 PostgreSQL 17+ 最佳實踐）
	db.SetMaxOpenConns(int(cfg.MaxConnections)) // 基於 CPU 核心數
	db.SetMaxIdleConns(int(cfg.MaxIdleConns))   // 保持最小連線數
	db.SetConnMaxLifetime(cfg.MaxLifetime)      // 連線輪換

	// 測試資料庫連線
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &DB{db: db}, nil
}

// Close 關閉資料庫連線池
// 應該在應用程式結束時呼叫
func (db *DB) Close() error {
	return db.db.Close()
}

// Health 檢查資料庫健康狀態
// 用於健康檢查端點，確保資料庫連線正常
// 會執行 ping 和簡單查詢來驗證連線
func (db *DB) Health(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, 1*time.Second)
	defer cancel()

	if err := db.db.PingContext(ctx); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	// 檢查是否可以執行簡單查詢
	var result int
	if err := db.db.QueryRowContext(ctx, "SELECT 1").Scan(&result); err != nil {
		return fmt.Errorf("database query check failed: %w", err)
	}

	return nil
}

// DB 返回底層的 sql.DB
// 用於需要直接存取資料庫的操作，特別是 sqlc
func (db *DB) DB() *sql.DB {
	return db.db
}

// BeginTx 開始一個新的交易
// 返回 sql.Tx 用於類型安全的交易操作
func (db *DB) BeginTx(ctx context.Context) (*sql.Tx, error) {
	return db.db.BeginTx(ctx, nil)
}

// Query 執行查詢並返回 sql.Rows
// 提供類型安全的查詢操作
func (db *DB) Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return db.db.QueryContext(ctx, query, args...)
}

// QueryRow 執行查詢並返回單一行
// 提供類型安全的單行查詢操作
func (db *DB) QueryRow(ctx context.Context, query string, args ...interface{}) *sql.Row {
	return db.db.QueryRowContext(ctx, query, args...)
}

// Exec 執行 SQL 命令
// 返回受影響的行數
func (db *DB) Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	return db.db.ExecContext(ctx, query, args...)
}

// Stats 返回連線池統計資訊
// 用於監控和調試連線池狀態
func (db *DB) Stats() sql.DBStats {
	return db.db.Stats()
}
