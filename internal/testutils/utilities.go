package testutils

import (
	"bytes"
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/internal/logger"
)

// CommonTestUsers provides pre-defined test users for different roles
var CommonTestUsers = struct {
	Admin   TestUser
	CISA    TestUser
	Company TestUser
}{
	Admin: TestUser{
		ID:       1,
		Username: "admin",
		Email:    "<EMAIL>",
		Role:     "SPO",
		Password: "AdminPass123!",
	},
	CISA: TestUser{
		ID:       2,
		Username: "cisa_user",
		Email:    "<EMAIL>",
		Role:     "CISA",
		Password: "CisaPass123!",
	},
	Company: TestUser{
		ID:       3,
		Username: "company_user",
		Email:    "<EMAIL>",
		Role:     "Company",
		Password: "CompanyPass123!",
	},
}

// AssertValidationErrors checks that validation errors are returned correctly
func AssertValidationErrors(t *testing.T, w *httptest.ResponseRecorder, expectedFields []string) {
	t.Helper()

	asserter := NewResponseAsserter(t, w)
	asserter.HasStatus(400)

	response := asserter.HasJSONBody()

	errors, ok := response["errors"].(map[string]interface{})
	if !ok {
		t.Errorf("response does not contain 'errors' field")
		return
	}

	for _, field := range expectedFields {
		if _, exists := errors[field]; !exists {
			t.Errorf("expected validation error for field '%s' not found", field)
		}
	}
}

// CreateTestContext creates a context with common test values
func CreateTestContext() context.Context {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "request_id", "test-request-id")
	ctx = context.WithValue(ctx, "trace_id", "test-trace-id")
	return ctx
}

// TestLogger creates a logger suitable for testing
func TestLogger() *logger.Logger {
	handler := slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	})
	return &logger.Logger{Logger: slog.New(handler)}
}

// Legacy request creation functions for backward compatibility
// These wrap the new HTTPRequestBuilder for existing tests

// CreateAdminRequest creates an HTTP request with admin authentication
func CreateAdminRequest(method, url string, body interface{}) *http.Request {
	return NewHTTPRequest(method, url).
		WithBody(body).
		WithAdminAuth().
		Build()
}

// CreateCISARequest creates an HTTP request with CISA authentication
func CreateCISARequest(method, url string, body interface{}, userID int32) *http.Request {
	return NewHTTPRequest(method, url).
		WithBody(body).
		WithCISAAuth(userID).
		Build()
}

// CreateCompanyRequest creates an HTTP request with company authentication
func CreateCompanyRequest(method, url string, body interface{}, userID, companyID int32) *http.Request {
	return NewHTTPRequest(method, url).
		WithBody(body).
		WithCompanyAuth(userID, companyID).
		Build()
}

// Additional legacy functions for backward compatibility

// CreateRequest creates a basic HTTP request without authentication
func CreateRequest(method, url string, body interface{}) *http.Request {
	return NewHTTPRequest(method, url).
		WithBody(body).
		Build()
}

// CreateRequestWithPathValues creates an HTTP request with path values
func CreateRequestWithPathValues(method, url string, body interface{}, pathValues map[string]string) *http.Request {
	builder := NewHTTPRequest(method, url).WithBody(body)
	for key, value := range pathValues {
		builder = builder.WithPathValue(key, value)
	}
	return builder.Build()
}

// AssertStatus asserts the HTTP status code
func AssertStatus(t *testing.T, recorder *httptest.ResponseRecorder, expectedStatus int) {
	t.Helper()
	if recorder.Code != expectedStatus {
		t.Errorf("Expected status %d, got %d. Response: %s", expectedStatus, recorder.Code, recorder.Body.String())
	}
}

// AssertSuccessResponse asserts a successful JSON response and returns the parsed data
func AssertSuccessResponse(t *testing.T, recorder *httptest.ResponseRecorder) map[string]interface{} {
	t.Helper()

	var response map[string]interface{}
	err := json.Unmarshal(recorder.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to parse response JSON: %v\nResponse body: %s", err, recorder.Body.String())
	}

	success, ok := response["success"].(bool)
	if !ok || !success {
		t.Errorf("Expected success to be true, got: %v", response)
	}

	return response
}

// AssertErrorResponse asserts an error response with expected message
func AssertErrorResponse(t *testing.T, recorder *httptest.ResponseRecorder, expectedMessage string) {
	t.Helper()

	var response map[string]interface{}
	err := json.Unmarshal(recorder.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to parse response JSON: %v\nResponse body: %s", err, recorder.Body.String())
	}

	success, _ := response["success"].(bool)
	if success {
		t.Error("Expected success to be false")
	}

	if errorInfo, ok := response["error"].(map[string]interface{}); ok {
		if message, ok := errorInfo["message"].(string); ok {
			if message != expectedMessage {
				t.Errorf("Expected error message %q, got %q", expectedMessage, message)
			}
		} else {
			t.Error("Error response missing message field")
		}
	} else {
		t.Error("Response missing error field")
	}
}

// Pointer helper functions for creating test data
// These are more specific than the generic helpers.go files

// StringPtr returns a pointer to the given string
func StringPtr(s string) *string {
	return &s
}

// Int32Ptr returns a pointer to the given int32
func Int32Ptr(i int32) *int32 {
	return &i
}

// CreateJSONRequest creates an HTTP request with JSON body
func CreateJSONRequest(t *testing.T, method, url string, body interface{}) *http.Request {
	t.Helper()
	return NewHTTPRequest(method, url).
		WithBody(body).
		WithHeader("Content-Type", "application/json").
		Build()
}

// CompareJSON compares two JSON-serializable objects for equality
func CompareJSON(t *testing.T, expected, actual interface{}) {
	t.Helper()

	expectedJSON, err := json.Marshal(expected)
	if err != nil {
		t.Fatalf("Failed to marshal expected value: %v", err)
	}

	actualJSON, err := json.Marshal(actual)
	if err != nil {
		t.Fatalf("Failed to marshal actual value: %v", err)
	}

	if !bytes.Equal(expectedJSON, actualJSON) {
		t.Errorf("JSON values do not match.\nExpected: %s\nActual: %s", expectedJSON, actualJSON)
	}
}

// Float64Ptr returns a pointer to a float64 literal
func Float64Ptr(f float64) *float64 {
	return &f
}

// BoolPtr returns a pointer to a bool literal
func BoolPtr(b bool) *bool {
	return &b
}

// TimePtr returns a pointer to a time literal
func TimePtr(t time.Time) *time.Time {
	return &t
}

// MockClock provides controllable time for testing
type MockClock struct {
	currentTime time.Time
}

// NewMockClock creates a new mock clock with the given time
func NewMockClock(t time.Time) *MockClock {
	return &MockClock{currentTime: t}
}

// Now returns the current mock time
func (m *MockClock) Now() time.Time {
	return m.currentTime
}

// Advance moves the mock time forward by the given duration
func (m *MockClock) Advance(duration time.Duration) {
	m.currentTime = m.currentTime.Add(duration)
}

// Set changes the mock time to the given time
func (m *MockClock) Set(t time.Time) {
	m.currentTime = t
}

// TestDataBuilder provides a fluent interface for building test data
type TestDataBuilder struct {
	data map[string]interface{}
}

// NewTestDataBuilder creates a new test data builder
func NewTestDataBuilder() *TestDataBuilder {
	return &TestDataBuilder{
		data: make(map[string]interface{}),
	}
}

// WithField adds a field to the test data
func (b *TestDataBuilder) WithField(key string, value interface{}) *TestDataBuilder {
	b.data[key] = value
	return b
}

// WithUser adds user data to the test data
func (b *TestDataBuilder) WithUser(user TestUser) *TestDataBuilder {
	b.data["user_id"] = user.ID
	b.data["username"] = user.Username
	b.data["email"] = user.Email
	b.data["role"] = user.Role
	return b
}

// Build returns the built test data
func (b *TestDataBuilder) Build() map[string]interface{} {
	return b.data
}

// BuildJSON returns the test data as JSON bytes
func (b *TestDataBuilder) BuildJSON() []byte {
	jsonData, _ := json.Marshal(b.data)
	return jsonData
}

// AssertionHelper provides common assertion patterns
type AssertionHelper struct {
	t *testing.T
}

// NewAssertionHelper creates a new assertion helper
func NewAssertionHelper(t *testing.T) *AssertionHelper {
	return &AssertionHelper{t: t}
}

// AssertEqual asserts that two values are equal
func (h *AssertionHelper) AssertEqual(expected, actual interface{}, message string) {
	h.t.Helper()
	if expected != actual {
		h.t.Errorf("%s: expected %v, got %v", message, expected, actual)
	}
}

// AssertNotEqual asserts that two values are not equal
func (h *AssertionHelper) AssertNotEqual(expected, actual interface{}, message string) {
	h.t.Helper()
	if expected == actual {
		h.t.Errorf("%s: expected values to be different, but both were %v", message, expected)
	}
}

// AssertNil asserts that a value is nil
func (h *AssertionHelper) AssertNil(value interface{}, message string) {
	h.t.Helper()
	if value != nil {
		h.t.Errorf("%s: expected nil, got %v", message, value)
	}
}

// AssertNotNil asserts that a value is not nil
func (h *AssertionHelper) AssertNotNil(value interface{}, message string) {
	h.t.Helper()
	if value == nil {
		h.t.Errorf("%s: expected non-nil value", message)
	}
}

// AssertTrue asserts that a condition is true
func (h *AssertionHelper) AssertTrue(condition bool, message string) {
	h.t.Helper()
	if !condition {
		h.t.Errorf("%s: expected true", message)
	}
}

// AssertFalse asserts that a condition is false
func (h *AssertionHelper) AssertFalse(condition bool, message string) {
	h.t.Helper()
	if condition {
		h.t.Errorf("%s: expected false", message)
	}
}

// AssertContains asserts that a string contains a substring
func (h *AssertionHelper) AssertContains(haystack, needle, message string) {
	h.t.Helper()
	if !bytes.Contains([]byte(haystack), []byte(needle)) {
		h.t.Errorf("%s: expected %q to contain %q", message, haystack, needle)
	}
}

// AssertNotContains asserts that a string does not contain a substring
func (h *AssertionHelper) AssertNotContains(haystack, needle, message string) {
	h.t.Helper()
	if bytes.Contains([]byte(haystack), []byte(needle)) {
		h.t.Errorf("%s: expected %q to not contain %q", message, haystack, needle)
	}
}

// TestTimeout provides timeout utilities for testing
type TestTimeout struct {
	duration time.Duration
}

// NewTestTimeout creates a new test timeout
func NewTestTimeout(duration time.Duration) *TestTimeout {
	return &TestTimeout{duration: duration}
}

// WithTimeout executes a function with a timeout
func (tt *TestTimeout) WithTimeout(t *testing.T, fn func()) {
	t.Helper()

	done := make(chan bool, 1)

	go func() {
		fn()
		done <- true
	}()

	select {
	case <-done:
		// Function completed successfully
	case <-time.After(tt.duration):
		t.Errorf("Test timed out after %v", tt.duration)
	}
}
