package testutils

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

// TestCase represents a generic test case structure for table-driven tests
type TestCase struct {
	Name           string
	SetupFunc      func() // Optional setup before test
	CleanupFunc    func() // Optional cleanup after test
	RequestFunc    func() *http.Request
	HandlerFunc    func(w http.ResponseWriter, r *http.Request)
	ExpectedStatus int
	ValidateFunc   func(t *testing.T, recorder *httptest.ResponseRecorder)
}

// RunTestCases executes a slice of test cases using table-driven testing pattern
// This follows the Architecture.md guidelines for comprehensive test coverage
func RunTestCases(t *testing.T, testCases []TestCase) {
	for _, tc := range testCases {
		tc := tc // Capture range variable for parallel execution
		t.Run(tc.Name, func(t *testing.T) {
			t.Parallel()

			// Run setup if provided
			if tc.SetupFunc != nil {
				tc.SetupFunc()
			}

			// Run cleanup after test if provided
			if tc.CleanupFunc != nil {
				defer tc.CleanupFunc()
			}

			// Create request and recorder
			req := tc.RequestFunc()
			recorder := httptest.NewRecorder()

			// Execute handler
			tc.HandlerFunc(recorder, req)

			// Check status code
			asserter := NewResponseAsserter(t, recorder)
			asserter.HasStatus(tc.ExpectedStatus)

			// Run additional validation if provided
			if tc.ValidateFunc != nil {
				tc.ValidateFunc(t, recorder)
			}
		})
	}
}

// HTTPTestCase represents a more specific HTTP test case with built-in assertions
type HTTPTestCase struct {
	Name           string
	Method         string
	URL            string
	Body           interface{}
	SetupAuth      func(*HTTPRequestBuilder) *HTTPRequestBuilder
	SetupRequest   func(*HTTPRequestBuilder) *HTTPRequestBuilder
	SetupMock      func() // Setup mock services
	CleanupFunc    func() // Cleanup after test
	ExpectedStatus int
	ExpectedError  string // If set, expects error response with this message
	ValidateFunc   func(t *testing.T, response map[string]interface{})
}

// RunHTTPTestCases executes HTTP-specific test cases with standardized patterns
func RunHTTPTestCases(t *testing.T, handler func(w http.ResponseWriter, r *http.Request), testCases []HTTPTestCase) {
	for _, tc := range testCases {
		tc := tc // Capture range variable
		t.Run(tc.Name, func(t *testing.T) {
			t.Parallel()

			// Setup mock if provided
			if tc.SetupMock != nil {
				tc.SetupMock()
			}

			// Cleanup if provided
			if tc.CleanupFunc != nil {
				defer tc.CleanupFunc()
			}

			// Build request
			builder := NewHTTPRequest(tc.Method, tc.URL).WithBody(tc.Body)

			// Apply auth setup if provided
			if tc.SetupAuth != nil {
				builder = tc.SetupAuth(builder)
			}

			// Apply additional request setup if provided
			if tc.SetupRequest != nil {
				builder = tc.SetupRequest(builder)
			}

			req := builder.Build()
			recorder := httptest.NewRecorder()

			// Execute handler
			handler(recorder, req)

			// Assert response
			asserter := NewResponseAsserter(t, recorder)
			asserter.HasStatus(tc.ExpectedStatus)

			if tc.ExpectedError != "" {
				// Expect error response
				asserter.IsErrorResponse(tc.ExpectedError)
			} else {
				// Expect success response
				response := asserter.IsSuccessResponse()

				// Run additional validation if provided
				if tc.ValidateFunc != nil {
					tc.ValidateFunc(t, response)
				}
			}
		})
	}
}

// BenchmarkCase represents a benchmark test case
type BenchmarkCase struct {
	Name        string
	SetupFunc   func() *http.Request
	HandlerFunc func(w http.ResponseWriter, r *http.Request)
}

// RunBenchmarkCases executes benchmark test cases
func RunBenchmarkCases(b *testing.B, benchCases []BenchmarkCase) {
	for _, bc := range benchCases {
		b.Run(bc.Name, func(b *testing.B) {
			req := bc.SetupFunc()

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				recorder := httptest.NewRecorder()
				bc.HandlerFunc(recorder, req)
			}
		})
	}
}

// FuzzTestCase represents a fuzz test case structure
type FuzzTestCase struct {
	Name         string
	FuzzFunc     func(f *testing.F)
	SeedInputs   []interface{}
	PropertyFunc func(t *testing.T, input interface{}) // Property to verify
}

// RunFuzzTestCases executes fuzz test cases following Go 1.18+ patterns
func RunFuzzTestCases(f *testing.F, fuzzCases []FuzzTestCase) {
	for _, fc := range fuzzCases {
		// Add seed inputs for this case
		for _, seed := range fc.SeedInputs {
			switch v := seed.(type) {
			case string:
				f.Add(v)
			case []byte:
				f.Add(v)
			case int:
				f.Add(v)
			case int32:
				f.Add(v)
			case int64:
				f.Add(v)
			case float32:
				f.Add(v)
			case float64:
				f.Add(v)
			case bool:
				f.Add(v)
			}
		}

		// Run fuzz function
		fc.FuzzFunc(f)
	}
}
