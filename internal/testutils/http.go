// Package testutils provides comprehensive testing utilities for HTTP handlers,
// database operations, authentication, and common test patterns following
// the architectural guidelines in Architecture.md
package testutils

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
)

// HTTPRequestBuilder provides a fluent interface for building HTTP requests for testing
type HTTPRequestBuilder struct {
	method      string
	url         string
	body        interface{}
	headers     map[string]string
	pathValues  map[string]string
	contextData map[interface{}]interface{}
}

// NewHTTPRequest creates a new HTTP request builder
func NewHTTPRequest(method, url string) *HTTPRequestBuilder {
	return &HTTPRequestBuilder{
		method:      method,
		url:         url,
		headers:     make(map[string]string),
		pathValues:  make(map[string]string),
		contextData: make(map[interface{}]interface{}),
	}
}

// WithBody sets the request body
func (b *HTTPRequestBuilder) WithBody(body interface{}) *HTTPRequestBuilder {
	b.body = body
	return b
}

// WithHeader adds a header to the request
func (b *HTTPRequestBuilder) WithHeader(key, value string) *HTTPRequestBuilder {
	b.headers[key] = value
	return b
}

// WithPathValue sets a path value for the request
func (b *HTTPRequestBuilder) WithPathValue(key, value string) *HTTPRequestBuilder {
	b.pathValues[key] = value
	return b
}

// WithContext sets a context value for the request
func (b *HTTPRequestBuilder) WithContext(key, value interface{}) *HTTPRequestBuilder {
	b.contextData[key] = value
	return b
}

// WithAuthContext sets authentication context data
// This is a generic method that allows packages to set their own auth context
func (b *HTTPRequestBuilder) WithAuthContext(key, value interface{}) *HTTPRequestBuilder {
	b.contextData[key] = value
	return b
}

// Build creates the HTTP request
func (b *HTTPRequestBuilder) Build() *http.Request {
	var bodyReader *bytes.Buffer

	if b.body != nil {
		switch v := b.body.(type) {
		case string:
			bodyReader = bytes.NewBufferString(v)
		case []byte:
			bodyReader = bytes.NewBuffer(v)
		default:
			bodyBytes, _ := json.Marshal(b.body)
			bodyReader = bytes.NewBuffer(bodyBytes)
		}
	}

	var req *http.Request
	if bodyReader != nil {
		req = httptest.NewRequest(b.method, b.url, bodyReader)
		req.Header.Set("Content-Type", "application/json")
	} else {
		req = httptest.NewRequest(b.method, b.url, nil)
	}

	// Set headers
	for key, value := range b.headers {
		req.Header.Set(key, value)
	}

	// Set path values
	for key, value := range b.pathValues {
		req.SetPathValue(key, value)
	}

	// Set context data
	ctx := req.Context()
	for key, value := range b.contextData {
		ctx = context.WithValue(ctx, key, value)
	}
	req = req.WithContext(ctx)

	return req
}

// NewResponseRecorder creates a new HTTP response recorder for testing
func NewResponseRecorder() *httptest.ResponseRecorder {
	return httptest.NewRecorder()
}

// ResponseAsserter provides fluent interface for asserting HTTP responses
type ResponseAsserter struct {
	t        *testing.T
	recorder *httptest.ResponseRecorder
}

// NewResponseAsserter creates a new response asserter
func NewResponseAsserter(t *testing.T, recorder *httptest.ResponseRecorder) *ResponseAsserter {
	t.Helper()
	return &ResponseAsserter{t: t, recorder: recorder}
}

// HasStatus asserts the response status code
func (a *ResponseAsserter) HasStatus(expected int) *ResponseAsserter {
	a.t.Helper()
	if a.recorder.Code != expected {
		a.t.Errorf("handler returned wrong status code: got %v want %v", a.recorder.Code, expected)
		a.t.Logf("response body: %s", a.recorder.Body.String())
	}
	return a
}

// HasContentType asserts the response content type
func (a *ResponseAsserter) HasContentType(expected string) *ResponseAsserter {
	a.t.Helper()
	actual := a.recorder.Header().Get("Content-Type")
	if actual != expected {
		a.t.Errorf("expected Content-Type %q, got %q", expected, actual)
	}
	return a
}

// HasJSONBody asserts the response has valid JSON and returns the parsed body
func (a *ResponseAsserter) HasJSONBody() map[string]interface{} {
	a.t.Helper()

	var response map[string]interface{}
	err := json.Unmarshal(a.recorder.Body.Bytes(), &response)
	if err != nil {
		a.t.Fatalf("Could not parse response JSON: %v\nResponse body: %s", err, a.recorder.Body.String())
	}

	return response
}

// IsSuccessResponse asserts the response indicates success
func (a *ResponseAsserter) IsSuccessResponse() map[string]interface{} {
	a.t.Helper()

	response := a.HasJSONBody()

	success, ok := response["success"].(bool)
	if !ok || !success {
		a.t.Errorf("Expected success to be true, got: %v", response)
	}

	return response
}

// IsErrorResponse asserts the response contains expected error
func (a *ResponseAsserter) IsErrorResponse(expectedMessage string) {
	a.t.Helper()

	response := a.HasJSONBody()

	success, _ := response["success"].(bool)
	if success {
		a.t.Error("Expected success to be false")
	}

	if errorInfo, ok := response["error"].(map[string]interface{}); ok {
		if message, ok := errorInfo["message"].(string); ok {
			if message != expectedMessage {
				a.t.Errorf("Expected error message %q, got %q", expectedMessage, message)
			}
		} else {
			a.t.Error("Error response missing message field")
		}
	} else {
		a.t.Error("Response missing error field")
	}
}

// Common test data constants
const (
	TestUserID    = int32(123)
	TestCompanyID = int32(456)
	TestUsername  = "testuser"
	TestEmail     = "<EMAIL>"
)
