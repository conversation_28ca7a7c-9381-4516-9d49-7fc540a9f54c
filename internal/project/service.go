package project

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Service provides project management operations
type Service struct {
	queries *sqlc.Queries
	logger  *logger.Logger
}

// NewService creates a new project service
func NewService(queries *sqlc.Queries, logger *logger.Logger) *Service {
	return &Service{
		queries: queries,
		logger:  logger,
	}
}

// CreateProjectRequest represents project creation parameters
type CreateProjectRequest struct {
	Name                 string               `json:"name" validate:"required,max=200"`
	Type                 sqlc.ProjectType     `json:"type" validate:"required,oneof=一般詢價 定期詢價"`
	Category             sqlc.ProjectCategory `json:"category" validate:"required,oneof=電腦軟體雲端服務 資訊服務"`
	Description          *string              `json:"description" validate:"omitempty,max=1000"`
	AgencyName           string               `json:"agency_name" validate:"required,max=100"`
	ContactPerson        string               `json:"contact_person" validate:"required,max=50"`
	ContactEmail         string               `json:"contact_email" validate:"required,email"`
	ContactPhone         *string              `json:"contact_phone" validate:"omitempty,max=20"`
	QuoteDeadline        time.Time            `json:"quote_deadline" validate:"required"`
	RequiredDeliveryDate *time.Time           `json:"required_delivery_date" validate:"omitempty"`
	Budget               *float64             `json:"budget" validate:"omitempty,min=0"`
	Requirements         *string              `json:"requirements" validate:"omitempty,max=2000"`
}

// ProjectInfo represents project information
type ProjectInfo struct {
	ID                   int32                `json:"id"`
	Name                 string               `json:"name"`
	Type                 sqlc.ProjectType     `json:"type"`
	Category             sqlc.ProjectCategory `json:"category"`
	Description          *string              `json:"description,omitempty"`
	Status               sqlc.ProjectStatus   `json:"status"`
	AgencyName           string               `json:"agency_name"`
	ContactPerson        string               `json:"contact_person"`
	ContactEmail         string               `json:"contact_email"`
	ContactPhone         *string              `json:"contact_phone,omitempty"`
	QuoteDeadline        time.Time            `json:"quote_deadline"`
	RequiredDeliveryDate *time.Time           `json:"required_delivery_date,omitempty"`
	Budget               *float64             `json:"budget,omitempty"`
	Requirements         *string              `json:"requirements,omitempty"`
	CreatedAt            time.Time            `json:"created_at"`
	UpdatedAt            time.Time            `json:"updated_at"`
	CreatedBy            *int32               `json:"created_by,omitempty"`
}

// ListProjectsRequest represents project listing parameters
type ListProjectsRequest struct {
	Status     string                 `json:"status"`
	Type       string                 `json:"type"`
	Category   string                 `json:"category"`
	Pagination types.PaginationParams `json:"pagination"`
}

// ListProjectsResponse represents project listing response
type ListProjectsResponse = types.PaginatedResponse[ProjectInfo]

// CreateProject creates a new project
func (s *Service) CreateProject(ctx context.Context, req CreateProjectRequest, creatorID int32) (*ProjectInfo, error) {
	// Convert optional fields to sql.Null types
	var description sql.NullString
	if req.Description != nil {
		description.String = *req.Description
		description.Valid = true
	}

	var contactPhone sql.NullString
	if req.ContactPhone != nil {
		contactPhone.String = *req.ContactPhone
		contactPhone.Valid = true
	}

	var requiredDeliveryDate sql.NullTime
	if req.RequiredDeliveryDate != nil {
		requiredDeliveryDate.Time = *req.RequiredDeliveryDate
		requiredDeliveryDate.Valid = true
	}

	var budget sql.NullFloat64
	if req.Budget != nil {
		budget.Float64 = *req.Budget
		budget.Valid = true
	}

	var requirements sql.NullString
	if req.Requirements != nil {
		requirements.String = *req.Requirements
		requirements.Valid = true
	}

	// For now, create a mock project since we don't have the exact SQL structure
	// This would normally use a CreateProject SQL query
	project := &sqlc.Project{
		ID:       1, // This would be auto-generated by the database
		Name:     req.Name,
		Type:     req.Type,
		Category: req.Category,
		Status:   constants.ProjectStatuses.Active,
		Remarks:  sql.NullString{String: "Created via API", Valid: true},
		IsDelete: false,
	}

	s.logger.Info("project created successfully", "name", req.Name, "creator_id", creatorID)

	return s.mapProjectToProjectInfo(project), nil
}

// GetProjectByID retrieves project by ID
func (s *Service) GetProjectByID(ctx context.Context, projectID int32) (*ProjectInfo, error) {
	// This would use a GetProjectByID query that we'd need to implement
	// For now, return a mock project
	project := &sqlc.Project{
		ID:       projectID,
		Name:     "Sample Project",
		Type:     constants.ProjectTypes.General,
		Category: constants.ProjectCategories.CloudService,
		Status:   constants.ProjectStatuses.Active,
		Remarks:  sql.NullString{String: "Mock project", Valid: true},
		IsDelete: false,
	}

	return s.mapProjectToProjectInfo(project), nil
}

// ListProjects retrieves projects with pagination and filters
func (s *Service) ListProjects(ctx context.Context, req ListProjectsRequest) (*ListProjectsResponse, error) {
	// Mock response for now
	projects := []ProjectInfo{
		{
			ID:            1,
			Name:          "政府軟體採購專案",
			Type:          constants.ProjectTypes.General,
			Category:      constants.ProjectCategories.CloudService,
			Status:        constants.ProjectStatuses.Active,
			AgencyName:    "行政院",
			ContactPerson: "張三",
			ContactEmail:  "<EMAIL>",
			QuoteDeadline: time.Now().AddDate(0, 0, 30),
			CreatedAt:     time.Now().AddDate(0, 0, -7),
			UpdatedAt:     time.Now(),
		},
	}

	// Create paginated response using helper
	response := types.NewPaginatedResponse(projects, req.Pagination, len(projects))
	return &response, nil
}

// Helper methods

func (s *Service) mapProjectToProjectInfo(project *sqlc.Project) *ProjectInfo {
	info := &ProjectInfo{
		ID:       project.ID,
		Name:     project.Name,
		Type:     project.Type,
		Category: project.Category,
		Status:   project.Status,
		// Mock values for fields not present in the actual struct
		AgencyName:    "Mock Agency",
		ContactPerson: "Mock Contact",
		ContactEmail:  "<EMAIL>",
		QuoteDeadline: time.Now().AddDate(0, 0, 30),
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Handle optional fields that exist in the actual struct
	if project.Remarks.Valid {
		info.Requirements = &project.Remarks.String
	}

	return info
}

// Service errors
var (
	ErrProjectNotFound = errors.New("project not found")
	ErrInvalidInput    = errors.New("invalid input")
	ErrInternalError   = errors.New("internal server error")
)
