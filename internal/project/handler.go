package project

import (
	"net/http"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/validator"
)

// Handler handles HTTP requests for project management
type Handler struct {
	projectService *Service
	logger         *logger.Logger
}

// NewHandler creates a new project handler
func NewHandler(projectService *Service, logger *logger.Logger) *Handler {
	return &Handler{
		projectService: projectService,
		logger:         logger,
	}
}

// CreateProject handles POST /api/v1/projects
func (h *Handler) CreateProject(w http.ResponseWriter, r *http.Request) {
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	var req CreateProjectRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	if err := h.validateCreateProjectRequest(&req); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	project, err := h.projectService.CreateProject(r.Context(), req, userID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, project)
}

// GetProject handles GET /api/v1/projects/{id}
func (h *Handler) GetProject(w http.ResponseWriter, r *http.Request) {
	projectID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	project, err := h.projectService.GetProjectByID(r.Context(), projectID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, project)
}

// ListProjects handles GET /api/v1/projects
func (h *Handler) ListProjects(w http.ResponseWriter, r *http.Request) {
	pagination := api.ParseRequestPagination(r)
	req := ListProjectsRequest{
		Status:     r.URL.Query().Get("status"),
		Type:       r.URL.Query().Get("type"),
		Category:   r.URL.Query().Get("category"),
		Pagination: pagination,
	}

	projects, err := h.projectService.ListProjects(r.Context(), req)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, projects)
}

// Validation methods

func (h *Handler) validateCreateProjectRequest(req *CreateProjectRequest) error {
	v := validator.NewValidator()

	v.Required("name", req.Name)
	v.MaxLength("name", req.Name, constants.ProjectNameMaxLength)

	v.Required("type", string(req.Type))
	v.OneOf("type", string(req.Type), constants.GetValidProjectTypes())

	v.Required("category", string(req.Category))
	v.OneOf("category", string(req.Category), constants.GetValidProjectCategories())

	v.Required("agency_name", req.AgencyName)
	v.MaxLength("agency_name", req.AgencyName, constants.CompanyNameMaxLength)

	v.Required("contact_person", req.ContactPerson)
	v.MaxLength("contact_person", req.ContactPerson, constants.CompanyOwnerMaxLength)

	v.Required("contact_email", req.ContactEmail)
	v.Email("contact_email", req.ContactEmail)

	// Optional fields
	if req.Description != nil {
		v.MaxLength("description", *req.Description, constants.ProjectDescriptionMaxLength)
	}
	if req.ContactPhone != nil {
		v.MaxLength("contact_phone", *req.ContactPhone, constants.MobileMaxLength)
	}
	if req.Requirements != nil {
		v.MaxLength("requirements", *req.Requirements, 2000)
	}

	if errs := v.Errors(); errs != nil {
		return errs
	}
	return nil
}

// Helper methods

func (h *Handler) handleServiceError(w http.ResponseWriter, err error) {
	api.HandleCommonServiceError(w, err, h.logger, "project service")
}
