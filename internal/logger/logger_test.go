package logger

import (
	"bytes"
	"context"
	"encoding/json"
	"log/slog"
	"os"
	"testing"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// Logger package provides concrete implementations, tests verify behavior

func TestNew(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		level    string
		expected slog.Level
	}{
		{
			name:     "debug level",
			level:    "debug",
			expected: slog.LevelDebug,
		},
		{
			name:     "info level",
			level:    "info",
			expected: slog.LevelInfo,
		},
		{
			name:     "warn level",
			level:    "warn",
			expected: slog.LevelWarn,
		},
		{
			name:     "error level",
			level:    "error",
			expected: slog.LevelError,
		},
		{
			name:     "invalid level defaults to info",
			level:    "invalid",
			expected: slog.LevelInfo,
		},
		{
			name:     "empty level defaults to info",
			level:    "",
			expected: slog.LevelInfo,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.<PERSON>()

			logger := New(tt.level)

			if logger == nil {
				t.Error("expected logger to be created, got nil")
				return
			}

			if logger.Logger == nil {
				t.Error("expected underlying slog.Logger to be set")
			}

			// Test that the logger can be used without panicking
			logger.Info("test message")
		})
	}
}

func TestParseLevel(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		level    string
		expected slog.Level
	}{
		{
			name:     "debug level",
			level:    "debug",
			expected: slog.LevelDebug,
		},
		{
			name:     "info level",
			level:    "info",
			expected: slog.LevelInfo,
		},
		{
			name:     "warn level",
			level:    "warn",
			expected: slog.LevelWarn,
		},
		{
			name:     "error level",
			level:    "error",
			expected: slog.LevelError,
		},
		{
			name:     "invalid level",
			level:    "invalid",
			expected: slog.LevelInfo,
		},
		{
			name:     "empty level",
			level:    "",
			expected: slog.LevelInfo,
		},
		{
			name:     "uppercase level",
			level:    "DEBUG",
			expected: slog.LevelInfo, // Should default to info for non-exact matches
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := parseLevel(tt.level)
			if result != tt.expected {
				t.Errorf("expected level %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestLogger_With(t *testing.T) {
	t.Parallel()

	// Capture output for testing
	var buf bytes.Buffer
	
	// Create a logger with custom handler for testing
	opts := &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}
	handler := slog.NewJSONHandler(&buf, opts)
	logger := &Logger{
		Logger: slog.New(handler),
	}

	// Test adding fields
	loggerWithFields := logger.With("key1", "value1", "key2", 42)

	if loggerWithFields == nil {
		t.Error("expected logger with fields to be created, got nil")
		return
	}

	// Log a message to test the fields are included
	loggerWithFields.Info("test message")

	// Parse the JSON output to verify fields are included
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Errorf("failed to parse log output as JSON: %v", err)
		return
	}

	// Check that the fields are present
	if logEntry["key1"] != "value1" {
		t.Errorf("expected key1 to be 'value1', got %v", logEntry["key1"])
	}

	if logEntry["key2"] != float64(42) { // JSON numbers are float64
		t.Errorf("expected key2 to be 42, got %v", logEntry["key2"])
	}

	if logEntry["msg"] != "test message" {
		t.Errorf("expected msg to be 'test message', got %v", logEntry["msg"])
	}
}

func TestLogger_WithContext(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupContext   func() context.Context
		expectedFields map[string]interface{}
	}{
		{
			name: "context with request ID",
			setupContext: func() context.Context {
				return context.WithValue(context.Background(), ContextKeyRequestID, "req-123")
			},
			expectedFields: map[string]interface{}{
				"request_id": "req-123",
			},
		},
		{
			name: "context with user ID",
			setupContext: func() context.Context {
				return context.WithValue(context.Background(), ContextKeyUserID, int32(456))
			},
			expectedFields: map[string]interface{}{
				"user_id": float64(456), // JSON numbers are float64
			},
		},
		{
			name: "context with both request ID and user ID",
			setupContext: func() context.Context {
				ctx := context.WithValue(context.Background(), ContextKeyRequestID, "req-789")
				return context.WithValue(ctx, ContextKeyUserID, int32(101))
			},
			expectedFields: map[string]interface{}{
				"request_id": "req-789",
				"user_id":    float64(101),
			},
		},
		{
			name: "empty context",
			setupContext: func() context.Context {
				return context.Background()
			},
			expectedFields: map[string]interface{}{},
		},
		{
			name: "context with wrong type values",
			setupContext: func() context.Context {
				ctx := context.WithValue(context.Background(), ContextKeyRequestID, 123) // Wrong type
				return context.WithValue(ctx, ContextKeyUserID, "wrong-type")            // Wrong type
			},
			expectedFields: map[string]interface{}{},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a new buffer for each test
			var buf bytes.Buffer
			opts := &slog.HandlerOptions{
				Level: slog.LevelDebug,
			}
			handler := slog.NewJSONHandler(&buf, opts)
			logger := &Logger{
				Logger: slog.New(handler),
			}

			ctx := tt.setupContext()
			loggerWithContext := logger.WithContext(ctx)

			if loggerWithContext == nil {
				t.Error("expected logger with context to be created, got nil")
				return
			}

			// Log a message to test the context fields are included
			loggerWithContext.Info("test message with context")

			// Parse the JSON output to verify fields are included
			var logEntry map[string]interface{}
			if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
				t.Errorf("failed to parse log output as JSON: %v", err)
				return
			}

			// Check that expected fields are present
			for key, expectedValue := range tt.expectedFields {
				if logEntry[key] != expectedValue {
					t.Errorf("expected %s to be %v, got %v", key, expectedValue, logEntry[key])
				}
			}

			// Check that unexpected fields are not present when context is empty
			if len(tt.expectedFields) == 0 {
				if _, exists := logEntry["request_id"]; exists {
					t.Error("did not expect request_id field in empty context")
				}
				if _, exists := logEntry["user_id"]; exists {
					t.Error("did not expect user_id field in empty context")
				}
			}
		})
	}
}

func TestContextKeys(t *testing.T) {
	t.Parallel()

	// Test that context keys are properly defined
	if ContextKeyRequestID != "request_id" {
		t.Errorf("expected ContextKeyRequestID to be 'request_id', got %s", ContextKeyRequestID)
	}

	if ContextKeyUserID != "user_id" {
		t.Errorf("expected ContextKeyUserID to be 'user_id', got %s", ContextKeyUserID)
	}
}

func TestLogger_Integration(t *testing.T) {
	t.Parallel()

	// Test that the logger works end-to-end with a buffer
	var buf bytes.Buffer
	opts := &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}
	handler := slog.NewJSONHandler(&buf, opts)
	logger := &Logger{
		Logger: slog.New(handler),
	}

	// Create context with values
	ctx := context.WithValue(context.Background(), ContextKeyRequestID, "integration-test")
	ctx = context.WithValue(ctx, ContextKeyUserID, int32(999))

	// Log with context
	loggerWithContext := logger.WithContext(ctx)
	loggerWithContext.Info("integration test message", "extra_field", "extra_value")

	// Verify it's valid JSON
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Errorf("failed to parse log output as JSON: %v", err)
		return
	}

	// Verify expected fields
	if logEntry["msg"] != "integration test message" {
		t.Errorf("expected msg to be 'integration test message', got %v", logEntry["msg"])
	}

	if logEntry["extra_field"] != "extra_value" {
		t.Errorf("expected extra_field to be 'extra_value', got %v", logEntry["extra_field"])
	}

	if logEntry["request_id"] != "integration-test" {
		t.Errorf("expected request_id to be 'integration-test', got %v", logEntry["request_id"])
	}

	if logEntry["user_id"] != float64(999) {
		t.Errorf("expected user_id to be 999, got %v", logEntry["user_id"])
	}

	// Verify level
	if logEntry["level"] != "INFO" {
		t.Errorf("expected level to be 'INFO', got %v", logEntry["level"])
	}

	// Verify source is included (AddSource: true)
	if _, exists := logEntry["source"]; !exists {
		t.Error("expected source field to be present")
	}
}
