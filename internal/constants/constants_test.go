package constants

import (
	"testing"

	"github.com/koopa0/pms-api-v2/sqlc"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// Constants package provides concrete values, tests verify behavior and consistency

func TestConstants(t *testing.T) {
	t.<PERSON>()

	t.<PERSON>("pagination constants", func(t *testing.T) {
		t.<PERSON>()

		if DefaultPage != 1 {
			t.<PERSON>("expected DefaultPage to be 1, got %d", DefaultPage)
		}
		if DefaultPageSize != 20 {
			t.<PERSON><PERSON><PERSON>("expected DefaultPageSize to be 20, got %d", DefaultPageSize)
		}
		if MaxPageSize != 100 {
			t.<PERSON><PERSON>("expected MaxPageSize to be 100, got %d", MaxPageSize)
		}
	})

	t.Run("field length constants", func(t *testing.T) {
		t.<PERSON>()

		// User related
		if UsernameMinLength != 3 {
			t.<PERSON><PERSON><PERSON>("expected UsernameMinLength to be 3, got %d", UsernameMinLength)
		}
		if UsernameMaxLength != 50 {
			t.<PERSON><PERSON><PERSON>("expected UsernameMaxLength to be 50, got %d", UsernameMaxLength)
		}
		if PasswordMinLength != 8 {
			t.Errorf("expected PasswordMinLength to be 8, got %d", PasswordMinLength)
		}

		// Company related
		if CompanyNameMaxLength != 100 {
			t.Errorf("expected CompanyNameMaxLength to be 100, got %d", CompanyNameMaxLength)
		}

		// Project related
		if ProjectNameMaxLength != 200 {
			t.Errorf("expected ProjectNameMaxLength to be 200, got %d", ProjectNameMaxLength)
		}
	})

	t.Run("HTTP constants", func(t *testing.T) {
		t.Parallel()

		// Context keys
		if ContextKeyUserID != "userID" {
			t.Errorf("expected ContextKeyUserID to be 'userID', got %s", ContextKeyUserID)
		}
		if ContextKeyUserRole != "userRole" {
			t.Errorf("expected ContextKeyUserRole to be 'userRole', got %s", ContextKeyUserRole)
		}

		// Cookie names
		if CookieNameToken != "pms_token" {
			t.Errorf("expected CookieNameToken to be 'pms_token', got %s", CookieNameToken)
		}

		// Content types
		if ContentTypeJSON != "application/json" {
			t.Errorf("expected ContentTypeJSON to be 'application/json', got %s", ContentTypeJSON)
		}
	})
}

func TestUserRoles(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		role     sqlc.UserRole
		expected sqlc.UserRole
	}{
		{
			name:     "SPO role",
			role:     UserRoles.SPO,
			expected: sqlc.UserRoleSPO,
		},
		{
			name:     "CISA role",
			role:     UserRoles.CISA,
			expected: sqlc.UserRoleCISA,
		},
		{
			name:     "Company role",
			role:     UserRoles.Company,
			expected: sqlc.UserRoleCompany,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.role != tt.expected {
				t.Errorf("expected %v, got %v", tt.expected, tt.role)
			}
		})
	}
}

func TestUserStatuses(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		status   sqlc.UserStatus
		expected sqlc.UserStatus
	}{
		{
			name:     "Approved status",
			status:   UserStatuses.Approved,
			expected: sqlc.UserStatusValue0,
		},
		{
			name:     "PendingChange status",
			status:   UserStatuses.PendingChange,
			expected: sqlc.UserStatusValue1,
		},
		{
			name:     "ChangeRejected status",
			status:   UserStatuses.ChangeRejected,
			expected: sqlc.UserStatusValue2,
		},
		{
			name:     "Deleted status",
			status:   UserStatuses.Deleted,
			expected: sqlc.UserStatusValue3,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.status != tt.expected {
				t.Errorf("expected %v, got %v", tt.expected, tt.status)
			}
		})
	}
}

func TestValidationFunctions(t *testing.T) {
	t.Parallel()

	t.Run("IsValidUserRole", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name     string
			role     string
			expected bool
		}{
			{
				name:     "valid SPO role",
				role:     string(UserRoles.SPO),
				expected: true,
			},
			{
				name:     "valid CISA role",
				role:     string(UserRoles.CISA),
				expected: true,
			},
			{
				name:     "valid Company role",
				role:     string(UserRoles.Company),
				expected: true,
			},
			{
				name:     "invalid role",
				role:     "invalid_role",
				expected: false,
			},
			{
				name:     "empty role",
				role:     "",
				expected: false,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				result := IsValidUserRole(tt.role)
				if result != tt.expected {
					t.Errorf("expected %v, got %v", tt.expected, result)
				}
			})
		}
	})

	t.Run("IsValidUserStatus", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name     string
			status   string
			expected bool
		}{
			{
				name:     "valid Approved status",
				status:   string(UserStatuses.Approved),
				expected: true,
			},
			{
				name:     "valid PendingChange status",
				status:   string(UserStatuses.PendingChange),
				expected: true,
			},
			{
				name:     "invalid status",
				status:   "invalid_status",
				expected: false,
			},
			{
				name:     "empty status",
				status:   "",
				expected: false,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				result := IsValidUserStatus(tt.status)
				if result != tt.expected {
					t.Errorf("expected %v, got %v", tt.expected, result)
				}
			})
		}
	})

	t.Run("IsValidCompanyType", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			companyType string
			expected    bool
		}{
			{
				name:        "valid Software type",
				companyType: string(CompanyTypes.Software),
				expected:    true,
			},
			{
				name:        "valid InfoService type",
				companyType: string(CompanyTypes.InfoService),
				expected:    true,
			},
			{
				name:        "invalid type",
				companyType: "invalid_type",
				expected:    false,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				result := IsValidCompanyType(tt.companyType)
				if result != tt.expected {
					t.Errorf("expected %v, got %v", tt.expected, result)
				}
			})
		}
	})

	t.Run("IsValidProjectStatus", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name     string
			status   string
			expected bool
		}{
			{
				name:     "valid Active status",
				status:   string(ProjectStatuses.Active),
				expected: true,
			},
			{
				name:     "valid Closed status",
				status:   string(ProjectStatuses.Closed),
				expected: true,
			},
			{
				name:     "invalid status",
				status:   "invalid_status",
				expected: false,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				result := IsValidProjectStatus(tt.status)
				if result != tt.expected {
					t.Errorf("expected %v, got %v", tt.expected, result)
				}
			})
		}
	})
}

func TestGetterFunctions(t *testing.T) {
	t.Parallel()

	t.Run("GetValidUserRoles", func(t *testing.T) {
		t.Parallel()

		roles := GetValidUserRoles()
		expectedRoles := []string{
			string(UserRoles.SPO),
			string(UserRoles.CISA),
			string(UserRoles.Company),
		}

		if len(roles) != len(expectedRoles) {
			t.Errorf("expected %d roles, got %d", len(expectedRoles), len(roles))
			return
		}

		for i, expected := range expectedRoles {
			if roles[i] != expected {
				t.Errorf("expected role %d to be %s, got %s", i, expected, roles[i])
			}
		}
	})

	t.Run("GetValidUserStatuses", func(t *testing.T) {
		t.Parallel()

		statuses := GetValidUserStatuses()
		expectedStatuses := []string{
			string(UserStatuses.Approved),
			string(UserStatuses.PendingChange),
			string(UserStatuses.ChangeRejected),
			string(UserStatuses.Deleted),
		}

		if len(statuses) != len(expectedStatuses) {
			t.Errorf("expected %d statuses, got %d", len(expectedStatuses), len(statuses))
			return
		}

		for i, expected := range expectedStatuses {
			if statuses[i] != expected {
				t.Errorf("expected status %d to be %s, got %s", i, expected, statuses[i])
			}
		}
	})

	t.Run("GetValidCompanyTypes", func(t *testing.T) {
		t.Parallel()

		types := GetValidCompanyTypes()
		expectedTypes := []string{
			string(CompanyTypes.Software),
			string(CompanyTypes.InfoService),
		}

		if len(types) != len(expectedTypes) {
			t.Errorf("expected %d types, got %d", len(expectedTypes), len(types))
			return
		}

		for i, expected := range expectedTypes {
			if types[i] != expected {
				t.Errorf("expected type %d to be %s, got %s", i, expected, types[i])
			}
		}
	})

	t.Run("GetValidProjectStatuses", func(t *testing.T) {
		t.Parallel()

		statuses := GetValidProjectStatuses()
		expectedStatuses := []string{
			string(ProjectStatuses.Active),
			string(ProjectStatuses.Closed),
		}

		if len(statuses) != len(expectedStatuses) {
			t.Errorf("expected %d statuses, got %d", len(expectedStatuses), len(statuses))
			return
		}

		for i, expected := range expectedStatuses {
			if statuses[i] != expected {
				t.Errorf("expected status %d to be %s, got %s", i, expected, statuses[i])
			}
		}
	})

	t.Run("GetValidQuoteStatuses", func(t *testing.T) {
		t.Parallel()

		statuses := GetValidQuoteStatuses()
		expectedStatuses := []string{
			string(QuoteStatuses.Pending),
			string(QuoteStatuses.Approved),
			string(QuoteStatuses.Rejected),
			string(QuoteStatuses.Resent),
		}

		if len(statuses) != len(expectedStatuses) {
			t.Errorf("expected %d statuses, got %d", len(expectedStatuses), len(statuses))
			return
		}

		for i, expected := range expectedStatuses {
			if statuses[i] != expected {
				t.Errorf("expected status %d to be %s, got %s", i, expected, statuses[i])
			}
		}
	})
}

func TestFieldNames(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		field    string
		expected string
	}{
		{
			name:     "Username field",
			field:    FieldNames.Username,
			expected: "username",
		},
		{
			name:     "Email field",
			field:    FieldNames.Email,
			expected: "email",
		},
		{
			name:     "CompanyName field",
			field:    FieldNames.CompanyName,
			expected: "company_name",
		},
		{
			name:     "ProjectName field",
			field:    FieldNames.ProjectName,
			expected: "name",
		},
		{
			name:     "Page field",
			field:    FieldNames.Page,
			expected: "page",
		},
		{
			name:     "ID field",
			field:    FieldNames.ID,
			expected: "id",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.field != tt.expected {
				t.Errorf("expected %s, got %s", tt.expected, tt.field)
			}
		})
	}
}

func TestErrorMessages(t *testing.T) {
	t.Parallel()

	// Test that error messages are not empty
	errorMessages := []string{
		ErrMsgUnauthorized,
		ErrMsgForbidden,
		ErrMsgInvalidJSON,
		ErrMsgNotFound,
		ErrMsgInternalError,
		ErrMsgInvalidUserID,
		ErrMsgAdminRequired,
		ErrMsgInvalidCredentials,
		ErrMsgUserNotFound,
		ErrMsgValidationFailed,
		ErrMsgInvalidEmail,
		ErrMsgProjectNotFound,
	}

	for i, msg := range errorMessages {
		if msg == "" {
			t.Errorf("error message at index %d is empty", i)
		}
	}
}

func TestSuccessMessages(t *testing.T) {
	t.Parallel()

	// Test that success messages are not empty
	successMessages := []string{
		MsgSuccess,
		MsgCreated,
		MsgUpdated,
		MsgDeleted,
		MsgLoggedIn,
		MsgLoggedOut,
		MsgPasswordChanged,
		MsgRegistered,
	}

	for i, msg := range successMessages {
		if msg == "" {
			t.Errorf("success message at index %d is empty", i)
		}
	}
}
