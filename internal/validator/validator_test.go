package validator

import (
	"testing"

	"github.com/koopa0/pms-api-v2/internal/constants"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// Validator package provides concrete implementations, tests verify behavior

func TestValidationErrors(t *testing.T) {
	t.<PERSON>()

	t.Run("Error method", func(t *testing.T) {
		t.<PERSON>()

		ve := ValidationErrors{
			"username": {"is required", "must be at least 3 characters"},
			"email":    {"must be a valid email address"},
		}

		errorStr := ve.Error()
		if errorStr == "" {
			t.<PERSON>rror("expected non-empty error string")
		}

		// Should contain field names and messages
		if !contains(errorStr, "username") {
			t.<PERSON>r("expected error string to contain 'username'")
		}
		if !contains(errorStr, "email") {
			t.Error("expected error string to contain 'email'")
		}
	})

	t.Run("HasErrors method", func(t *testing.T) {
		t.<PERSON>()

		tests := []struct {
			name     string
			errors   ValidationErrors
			expected bool
		}{
			{
				name:     "empty errors",
				errors:   ValidationErrors{},
				expected: false,
			},
			{
				name:     "nil errors",
				errors:   nil,
				expected: false,
			},
			{
				name: "has errors",
				errors: ValidationErrors{
					"field": {"error message"},
				},
				expected: true,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				result := tt.errors.HasErrors()
				if result != tt.expected {
					t.Errorf("expected %v, got %v", tt.expected, result)
				}
			})
		}
	})

	t.Run("ToMap method", func(t *testing.T) {
		t.Parallel()

		ve := ValidationErrors{
			"username": {"is required"},
			"email":    {"invalid format"},
		}

		result := ve.ToMap()
		if len(result) != 2 {
			t.Errorf("expected 2 fields, got %d", len(result))
		}

		if result["username"] == nil {
			t.Error("expected username field in result")
		}
		if result["email"] == nil {
			t.Error("expected email field in result")
		}
	})
}

func TestValidator(t *testing.T) {
	t.Parallel()

	t.Run("NewValidator", func(t *testing.T) {
		t.Parallel()

		v := NewValidator()
		if v == nil {
			t.Error("expected validator to be created")
		}

		if v.Errors() != nil {
			t.Error("expected no errors initially")
		}
	})

	t.Run("AddError", func(t *testing.T) {
		t.Parallel()

		v := NewValidator()
		v.AddError("field1", "error1")
		v.AddError("field1", "error2")
		v.AddError("field2", "error3")

		errors := v.Errors()
		if len(errors) != 2 {
			t.Errorf("expected 2 fields with errors, got %d", len(errors))
		}

		if len(errors["field1"]) != 2 {
			t.Errorf("expected 2 errors for field1, got %d", len(errors["field1"]))
		}

		if len(errors["field2"]) != 1 {
			t.Errorf("expected 1 error for field2, got %d", len(errors["field2"]))
		}
	})

	t.Run("AddFieldError", func(t *testing.T) {
		t.Parallel()

		v := NewValidator()
		v.AddFieldError("field", "must be at least %d characters", 5)

		errors := v.Errors()
		if len(errors["field"]) != 1 {
			t.Errorf("expected 1 error, got %d", len(errors["field"]))
		}

		expectedMsg := "must be at least 5 characters"
		if errors["field"][0] != expectedMsg {
			t.Errorf("expected %q, got %q", expectedMsg, errors["field"][0])
		}
	})
}

func TestValidationMethods(t *testing.T) {
	t.Parallel()

	t.Run("Required", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			value       string
			expectError bool
		}{
			{"empty string", "", true},
			{"whitespace only", "   ", true},
			{"valid value", "test", false},
			{"value with spaces", " test ", false},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				v.Required("field", tt.value)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})

	t.Run("MinLength", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			value       string
			min         int
			expectError bool
		}{
			{"too short", "ab", 3, true},
			{"exact length", "abc", 3, false},
			{"longer than min", "abcd", 3, false},
			{"empty string", "", 1, true},
			{"whitespace trimmed", "  a  ", 3, true}, // trimmed to "a"
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				v.MinLength("field", tt.value, tt.min)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})

	t.Run("MaxLength", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			value       string
			max         int
			expectError bool
		}{
			{"within limit", "abc", 5, false},
			{"at limit", "abcde", 5, false},
			{"exceeds limit", "abcdef", 5, true},
			{"empty string", "", 5, false},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				v.MaxLength("field", tt.value, tt.max)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})

	t.Run("Email", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			value       string
			expectError bool
		}{
			{"valid email", "<EMAIL>", false},
			{"valid email with subdomain", "<EMAIL>", false},
			{"invalid email - no @", "testexample.com", true},
			{"invalid email - no domain", "test@", true},
			{"invalid email - no TLD", "test@example", true},
			{"empty string", "", false}, // Email validation skips empty strings
			{"invalid format", "not-an-email", true},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				v.Email("field", tt.value)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})

	t.Run("UnifiedBusinessNo", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			value       string
			expectError bool
		}{
			{"empty string", "", false},         // Skips validation for empty
			{"too short", "1234567", true},
			{"too long", "123456789", true},
			{"contains letters", "1234567a", true},
			{"invalid checksum", "12345678", true},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				v.UnifiedBusinessNo("field", tt.value)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})

	t.Run("OneOf", func(t *testing.T) {
		t.Parallel()

		allowed := []string{"option1", "option2", "option3"}

		tests := []struct {
			name        string
			value       string
			expectError bool
		}{
			{"valid option", "option1", false},
			{"another valid option", "option3", false},
			{"invalid option", "option4", true},
			{"empty string", "", false}, // Skips validation for empty
			{"case sensitive", "Option1", true},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				v.OneOf("field", tt.value, allowed)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})

	t.Run("Password", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			value       string
			expectError bool
		}{
			{"valid password", "password123", false},
			{"too short", "12345", true},
			{"minimum length", "123456", false},
			{"empty string", "", false}, // Skips validation for empty
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				v.Password("field", tt.value)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})
}

func TestValidationRules(t *testing.T) {
	t.Parallel()

	t.Run("ValidateUserFields", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name        string
			username    string
			email       string
			backupEmail *string
			jobTitle    *string
			mobile      *string
			expectError bool
		}{
			{
				name:        "valid fields",
				username:    "testuser",
				email:       "<EMAIL>",
				expectError: false,
			},
			{
				name:        "missing username",
				username:    "",
				email:       "<EMAIL>",
				expectError: true,
			},
			{
				name:        "invalid email",
				username:    "testuser",
				email:       "invalid-email",
				expectError: true,
			},
			{
				name:        "invalid backup email",
				username:    "testuser",
				email:       "<EMAIL>",
				backupEmail: stringPtr("invalid-backup"),
				expectError: true,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				ValidateUserFields(v, tt.username, tt.email, tt.backupEmail, tt.jobTitle, tt.mobile)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})

	t.Run("ValidateCompanyFields", func(t *testing.T) {
		t.Parallel()

		v := NewValidator()
		ValidateCompanyFields(v, "Test Company", "Owner Name", "Contact Person", nil, nil)

		if v.Errors() != nil {
			t.Error("expected no errors for valid company fields")
		}

		// Test with missing required fields
		v2 := NewValidator()
		ValidateCompanyFields(v2, "", "", "", nil, nil)

		if v2.Errors() == nil {
			t.Error("expected errors for missing required fields")
		}
	})

	t.Run("ValidatePasswordFields", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name          string
			password      string
			isNewPassword bool
			expectError   bool
		}{
			{
				name:          "valid password",
				password:      "validpassword123",
				isNewPassword: false,
				expectError:   false,
			},
			{
				name:          "too short password",
				password:      "short",
				isNewPassword: false,
				expectError:   true,
			},
			{
				name:          "empty password",
				password:      "",
				isNewPassword: true,
				expectError:   true,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				v := NewValidator()
				ValidatePasswordFields(v, tt.password, tt.isNewPassword)

				hasError := v.Errors() != nil
				if hasError != tt.expectError {
					t.Errorf("expected error: %v, got error: %v", tt.expectError, hasError)
				}
			})
		}
	})

	t.Run("ValidatePaginationParams", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name             string
			page             int
			pageSize         int
			expectedPage     int
			expectedPageSize int
		}{
			{
				name:             "valid params",
				page:             2,
				pageSize:         10,
				expectedPage:     2,
				expectedPageSize: 10,
			},
			{
				name:             "invalid page",
				page:             0,
				pageSize:         10,
				expectedPage:     constants.DefaultPage,
				expectedPageSize: 10,
			},
			{
				name:             "invalid page size",
				page:             1,
				pageSize:         0,
				expectedPage:     1,
				expectedPageSize: constants.DefaultPageSize,
			},
			{
				name:             "page size too large",
				page:             1,
				pageSize:         200,
				expectedPage:     1,
				expectedPageSize: constants.MaxPageSize,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				page, pageSize := ValidatePaginationParams(tt.page, tt.pageSize)

				if page != tt.expectedPage {
					t.Errorf("expected page %d, got %d", tt.expectedPage, page)
				}
				if pageSize != tt.expectedPageSize {
					t.Errorf("expected pageSize %d, got %d", tt.expectedPageSize, pageSize)
				}
			})
		}
	})

	t.Run("ValidateEnum functions", func(t *testing.T) {
		t.Parallel()

		// Test ValidateUserRole
		v := NewValidator()
		ValidateUserRole(v, string(constants.UserRoles.SPO))
		if v.Errors() != nil {
			t.Error("expected no errors for valid user role")
		}

		v2 := NewValidator()
		ValidateUserRole(v2, "invalid_role")
		if v2.Errors() == nil {
			t.Error("expected error for invalid user role")
		}

		// Test ValidateUserStatus
		v3 := NewValidator()
		ValidateUserStatus(v3, constants.UserStatuses.Approved)
		if v3.Errors() != nil {
			t.Error("expected no errors for valid user status")
		}

		// Test ValidateCompanyType
		v4 := NewValidator()
		ValidateCompanyType(v4, string(constants.CompanyTypes.Software))
		if v4.Errors() != nil {
			t.Error("expected no errors for valid company type")
		}
	})
}

func TestCommonValidationRules(t *testing.T) {
	t.Parallel()

	t.Run("RequiredString", func(t *testing.T) {
		t.Parallel()

		v := NewValidator()
		CommonValidationRules.RequiredString(v, "field", "value")
		if v.Errors() != nil {
			t.Error("expected no errors for non-empty string")
		}

		v2 := NewValidator()
		CommonValidationRules.RequiredString(v2, "field", "")
		if v2.Errors() == nil {
			t.Error("expected error for empty string")
		}
	})

	t.Run("OptionalString", func(t *testing.T) {
		t.Parallel()

		v := NewValidator()
		CommonValidationRules.OptionalString(v, "field", stringPtr("short"), 10)
		if v.Errors() != nil {
			t.Error("expected no errors for string within limit")
		}

		v2 := NewValidator()
		CommonValidationRules.OptionalString(v2, "field", stringPtr("this is a very long string"), 5)
		if v2.Errors() == nil {
			t.Error("expected error for string exceeding limit")
		}

		// Test with nil pointer
		v3 := NewValidator()
		CommonValidationRules.OptionalString(v3, "field", nil, 5)
		if v3.Errors() != nil {
			t.Error("expected no errors for nil string pointer")
		}
	})

	t.Run("RequiredEmail", func(t *testing.T) {
		t.Parallel()

		v := NewValidator()
		CommonValidationRules.RequiredEmail(v, "field", "<EMAIL>")
		if v.Errors() != nil {
			t.Error("expected no errors for valid email")
		}

		v2 := NewValidator()
		CommonValidationRules.RequiredEmail(v2, "field", "")
		if v2.Errors() == nil {
			t.Error("expected error for empty email")
		}

		v3 := NewValidator()
		CommonValidationRules.RequiredEmail(v3, "field", "invalid-email")
		if v3.Errors() == nil {
			t.Error("expected error for invalid email format")
		}
	})

	t.Run("OptionalEmail", func(t *testing.T) {
		t.Parallel()

		v := NewValidator()
		CommonValidationRules.OptionalEmail(v, "field", stringPtr("<EMAIL>"))
		if v.Errors() != nil {
			t.Error("expected no errors for valid optional email")
		}

		v2 := NewValidator()
		CommonValidationRules.OptionalEmail(v2, "field", stringPtr("invalid-email"))
		if v2.Errors() == nil {
			t.Error("expected error for invalid optional email")
		}

		// Test with nil pointer
		v3 := NewValidator()
		CommonValidationRules.OptionalEmail(v3, "field", nil)
		if v3.Errors() != nil {
			t.Error("expected no errors for nil email pointer")
		}
	})
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
		func() bool {
			for i := 1; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
			return false
		}())))
}
