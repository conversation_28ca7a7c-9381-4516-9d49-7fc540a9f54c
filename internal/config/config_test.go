package config

import (
	"os"
	"testing"
	"time"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// Config package provides concrete implementations, tests verify behavior

func TestLoad(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		envVars     map[string]string
		expectError bool
		validate    func(*testing.T, *Config)
	}{
		{
			name: "default configuration",
			envVars: map[string]string{
				"JWT_SECRET_KEY": "test-secret-key",
			},
			expectError: false,
			validate: func(t *testing.T, cfg *Config) {
				if cfg.Database.MaxConnections != DefaultMaxConnections {
					t.Errorf("expected MaxConnections %d, got %d", DefaultMaxConnections, cfg.Database.MaxConnections)
				}
				if cfg.Server.Port != DefaultServerPort {
					t.Errorf("expected Port %s, got %s", DefaultServerPort, cfg.Server.Port)
				}
				if cfg.JWT.Issuer != DefaultIssuer {
					t.Errorf("expected Issuer %s, got %s", DefaultIssuer, cfg.JWT.Issuer)
				}
				if cfg.Env != DefaultEnvironment {
					t.Errorf("expected Environment %s, got %s", DefaultEnvironment, cfg.Env)
				}
			},
		},
		{
			name: "custom configuration",
			envVars: map[string]string{
				"JWT_SECRET_KEY":              "custom-secret",
				"DATABASE_MAX_CONNECTIONS":    "50",
				"SERVER_PORT":                 "9090",
				"JWT_ISSUER":                  "custom-issuer",
				"ENVIRONMENT":                 "production",
				"LOG_LEVEL":                   "debug",
				"SERVER_READ_TIMEOUT":         "30s",
				"JWT_ACCESS_TOKEN_EXPIRY":     "2h",
				"DATABASE_MAX_IDLE_CONNECTIONS": "10",
			},
			expectError: false,
			validate: func(t *testing.T, cfg *Config) {
				if cfg.Database.MaxConnections != 50 {
					t.Errorf("expected MaxConnections 50, got %d", cfg.Database.MaxConnections)
				}
				if cfg.Server.Port != "9090" {
					t.Errorf("expected Port 9090, got %s", cfg.Server.Port)
				}
				if cfg.JWT.Issuer != "custom-issuer" {
					t.Errorf("expected Issuer custom-issuer, got %s", cfg.JWT.Issuer)
				}
				if cfg.Env != "production" {
					t.Errorf("expected Environment production, got %s", cfg.Env)
				}
				if cfg.LogLevel != "debug" {
					t.Errorf("expected LogLevel debug, got %s", cfg.LogLevel)
				}
				if cfg.Server.ReadTimeout != 30*time.Second {
					t.Errorf("expected ReadTimeout 30s, got %v", cfg.Server.ReadTimeout)
				}
				if cfg.JWT.AccessTokenExpiry != 2*time.Hour {
					t.Errorf("expected AccessTokenExpiry 2h, got %v", cfg.JWT.AccessTokenExpiry)
				}
			},
		},
		{
			name:        "missing JWT secret key",
			envVars:     map[string]string{},
			expectError: true,
			validate:    nil,
		},
		{
			name: "invalid duration format",
			envVars: map[string]string{
				"JWT_SECRET_KEY":      "test-secret",
				"SERVER_READ_TIMEOUT": "invalid-duration",
			},
			expectError: false,
			validate: func(t *testing.T, cfg *Config) {
				// Should fall back to default when parsing fails
				expectedDefault, _ := time.ParseDuration(DefaultReadTimeout)
				if cfg.Server.ReadTimeout != expectedDefault {
					t.Errorf("expected ReadTimeout %v (default), got %v", expectedDefault, cfg.Server.ReadTimeout)
				}
			},
		},
		{
			name: "CORS configuration",
			envVars: map[string]string{
				"JWT_SECRET_KEY":       "test-secret",
				"CORS_ALLOWED_ORIGINS": "http://localhost:3000,https://example.com",
				"CORS_ALLOWED_METHODS": "GET,POST,PUT",
				"CORS_ALLOWED_HEADERS": "Content-Type,Authorization,X-Custom",
			},
			expectError: false,
			validate: func(t *testing.T, cfg *Config) {
				expectedOrigins := []string{"http://localhost:3000", "https://example.com"}
				if len(cfg.CORS.AllowedOrigins) != len(expectedOrigins) {
					t.Errorf("expected %d origins, got %d", len(expectedOrigins), len(cfg.CORS.AllowedOrigins))
				}
				for i, origin := range expectedOrigins {
					if i < len(cfg.CORS.AllowedOrigins) && cfg.CORS.AllowedOrigins[i] != origin {
						t.Errorf("expected origin %s, got %s", origin, cfg.CORS.AllowedOrigins[i])
					}
				}
			},
		},
		{
			name: "email configuration",
			envVars: map[string]string{
				"JWT_SECRET_KEY": "test-secret",
				"SMTP_HOST":      "custom.smtp.com",
				"SMTP_PORT":      "465",
				"SMTP_USERNAME":  "<EMAIL>",
				"SMTP_PASSWORD":  "password123",
				"SMTP_FROM":      "<EMAIL>",
			},
			expectError: false,
			validate: func(t *testing.T, cfg *Config) {
				if cfg.Email.Host != "custom.smtp.com" {
					t.Errorf("expected SMTP host custom.smtp.com, got %s", cfg.Email.Host)
				}
				if cfg.Email.Port != 465 {
					t.Errorf("expected SMTP port 465, got %d", cfg.Email.Port)
				}
				if cfg.Email.Username != "<EMAIL>" {
					t.Errorf("expected <NAME_EMAIL>, got %s", cfg.Email.Username)
				}
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up environment variables
			for key, value := range tt.envVars {
				os.Setenv(key, value)
			}

			// Clean up after test
			defer func() {
				for key := range tt.envVars {
					os.Unsetenv(key)
				}
			}()

			cfg, err := Load()

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if cfg == nil {
				t.Error("expected config but got nil")
				return
			}

			if tt.validate != nil {
				tt.validate(t, cfg)
			}
		})
	}
}

func TestConfig_IsProduction(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		env      string
		expected bool
	}{
		{
			name:     "production environment",
			env:      EnvironmentProduction,
			expected: true,
		},
		{
			name:     "development environment",
			env:      EnvironmentDevelopment,
			expected: false,
		},
		{
			name:     "custom environment",
			env:      "staging",
			expected: false,
		},
		{
			name:     "empty environment",
			env:      "",
			expected: false,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			cfg := &Config{Env: tt.env}
			result := cfg.IsProduction()

			if result != tt.expected {
				t.Errorf("expected IsProduction() = %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestConfig_IsDevelopment(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		env      string
		expected bool
	}{
		{
			name:     "development environment",
			env:      EnvironmentDevelopment,
			expected: true,
		},
		{
			name:     "production environment",
			env:      EnvironmentProduction,
			expected: false,
		},
		{
			name:     "custom environment",
			env:      "staging",
			expected: false,
		},
		{
			name:     "empty environment",
			env:      "",
			expected: false,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			cfg := &Config{Env: tt.env}
			result := cfg.IsDevelopment()

			if result != tt.expected {
				t.Errorf("expected IsDevelopment() = %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestHelperFunctions(t *testing.T) {
	t.Parallel()

	t.Run("getEnv", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name         string
			key          string
			defaultValue string
			envValue     string
			expected     string
		}{
			{
				name:         "environment variable exists",
				key:          "TEST_ENV_VAR",
				defaultValue: "default",
				envValue:     "custom",
				expected:     "custom",
			},
			{
				name:         "environment variable does not exist",
				key:          "NON_EXISTENT_VAR",
				defaultValue: "default",
				envValue:     "",
				expected:     "default",
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				if tt.envValue != "" {
					os.Setenv(tt.key, tt.envValue)
					defer os.Unsetenv(tt.key)
				}

				result := getEnv(tt.key, tt.defaultValue)
				if result != tt.expected {
					t.Errorf("expected %s, got %s", tt.expected, result)
				}
			})
		}
	})

	t.Run("getEnvAsInt", func(t *testing.T) {
		t.Parallel()

		tests := []struct {
			name         string
			key          string
			defaultValue int
			envValue     string
			expected     int
		}{
			{
				name:         "valid integer",
				key:          "TEST_INT_VAR",
				defaultValue: 10,
				envValue:     "25",
				expected:     25,
			},
			{
				name:         "invalid integer",
				key:          "INVALID_INT_VAR",
				defaultValue: 10,
				envValue:     "not-a-number",
				expected:     10,
			},
		}

		for _, tt := range tests {
			tt := tt
			t.Run(tt.name, func(t *testing.T) {
				t.Parallel()

				if tt.envValue != "" {
					os.Setenv(tt.key, tt.envValue)
					defer os.Unsetenv(tt.key)
				}

				result := getEnvAsInt(tt.key, tt.defaultValue)
				if result != tt.expected {
					t.Errorf("expected %d, got %d", tt.expected, result)
				}
			})
		}
	})
}
