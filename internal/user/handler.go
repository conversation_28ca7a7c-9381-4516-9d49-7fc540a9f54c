// Package user 提供用戶管理相關的 HTTP 處理器
// 負責處理用戶相關的 REST API 端點，包含用戶資料 CRUD、密碼變更等功能
package user

import (
	"context"
	"errors"
	"net/http"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/validator"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Service defines the interface that the handler needs from the user service
// Following Architecture.md principle: test code should define interfaces based on what it needs
type Service interface {
	GetUserByID(ctx context.Context, userID int32) (*Info, error)
	UpdateUser(ctx context.Context, userID int32, req UpdateUserRequest) (*Info, error)
	ChangePassword(ctx context.Context, userID int32, req ChangePasswordRequest) error
	ListUsers(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error)
	UpdateUserStatus(ctx context.Context, userID int32, status sqlc.UserStatus) (*Info, error)
}

// Handler 用戶 HTTP 處理器
// 負責處理所有用戶相關的 HTTP 請求，包含身份驗證、授權檢查和輸入驗證
type Handler struct {
	userService Service        // 用戶業務邏輯服務
	logger      *logger.Logger // 結構化日誌記錄器
}

// NewHandler 創建新的用戶處理器實例
// 注入必要的依賴項，遵循依賴注入模式
func NewHandler(userService UserService, logger *logger.Logger) *Handler {
	return &Handler{
		userService: userService,
		logger:      logger,
	}
}

// GetUserProfile 處理 GET /api/users/profile
// 獲取當前登入用戶的個人資料
func (h *Handler) GetUserProfile(w http.ResponseWriter, r *http.Request) {
	userID, ok := auth.GetUserIDFromContext(r.Context())
	if !ok {
		api.Unauthorized(w, constants.ErrMsgUnauthorized)
		return
	}

	user, err := h.userService.GetUserByID(r.Context(), userID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, user)
}

// UpdateUserProfile 處理 PUT /api/users/profile
// 更新當前登入用戶的個人資料
func (h *Handler) UpdateUserProfile(w http.ResponseWriter, r *http.Request) {
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	var req UpdateUserRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	if err := h.validateUpdateUserRequest(&req); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	user, err := h.userService.UpdateUser(r.Context(), userID, req)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, user)
}

// ChangePassword 處理 POST /api/users/change-password
// 變更當前登入用戶的密碼
func (h *Handler) ChangePassword(w http.ResponseWriter, r *http.Request) {
	userID, ok := auth.GetUserIDFromContext(r.Context())
	if !ok {
		api.Unauthorized(w, constants.ErrMsgUnauthorized)
		return
	}

	var req ChangePasswordRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	if err := h.validateChangePasswordRequest(&req); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	if err := h.userService.ChangePassword(r.Context(), userID, req); err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, map[string]string{"message": constants.MsgPasswordChanged})
}

// ListUsers handles GET /api/users (admin only)
func (h *Handler) ListUsers(w http.ResponseWriter, r *http.Request) {
	if _, ok := auth.RequireAdmin(w, r); !ok {
		return
	}

	pagination := api.ParseRequestPagination(r)
	req := ListUsersRequest{
		Role:       sqlc.UserRole(r.URL.Query().Get("role")),
		Status:     sqlc.UserStatus(r.URL.Query().Get("status")),
		Pagination: pagination,
	}

	users, err := h.userService.ListUsers(r.Context(), req)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, users)
}

// GetUserByID handles GET /api/users/{id} (admin only)
func (h *Handler) GetUserByID(w http.ResponseWriter, r *http.Request) {
	if _, ok := auth.RequireAdmin(w, r); !ok {
		return
	}

	userID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	user, err := h.userService.GetUserByID(r.Context(), userID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, user)
}

// UpdateUserStatus handles PATCH /api/users/{id}/status (admin only)
func (h *Handler) UpdateUserStatus(w http.ResponseWriter, r *http.Request) {
	if _, ok := auth.RequireAdmin(w, r); !ok {
		return
	}

	userID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	var req struct {
		Status sqlc.UserStatus `json:"status"`
	}
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	if err := h.validateUserStatus(req.Status); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	user, err := h.userService.UpdateUserStatus(r.Context(), userID, req.Status)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(w, user)
}

// 驗證相關方法

// validateUpdateUserRequest 驗證用戶資料更新請求
// 確保輸入數據符合業務規則和技術約束
func (h *Handler) validateUpdateUserRequest(req *UpdateUserRequest) error {
	v := validator.NewValidator()

	v.Required(constants.FieldNames.Username, req.Username)
	v.MinLength(constants.FieldNames.Username, req.Username, constants.UsernameMinLength)
	v.MaxLength(constants.FieldNames.Username, req.Username, constants.UsernameMaxLength)

	v.Required(constants.FieldNames.Email, req.Email)
	v.Email(constants.FieldNames.Email, req.Email)

	if req.BackupEmail != nil {
		v.Email(constants.FieldNames.BackupEmail, *req.BackupEmail)
	}

	if req.JobTitle != nil {
		v.MaxLength(constants.FieldNames.JobTitle, *req.JobTitle, constants.JobTitleMaxLength)
	}

	if req.Mobile != nil {
		// 基本手機號碼驗證 - 檢查長度不超過限制
		v.MaxLength(constants.FieldNames.Mobile, *req.Mobile, constants.MobileMaxLength)
	}

	if errs := v.Errors(); errs != nil {
		return errs
	}
	return nil
}

// validateChangePasswordRequest 驗證密碼變更請求
// 確保密碼符合安全性要求
func (h *Handler) validateChangePasswordRequest(req *ChangePasswordRequest) error {
	v := validator.NewValidator()

	v.Required(constants.FieldNames.OldPassword, req.OldPassword)
	v.MinLength(constants.FieldNames.OldPassword, req.OldPassword, 1)

	v.Required(constants.FieldNames.NewPassword, req.NewPassword)
	v.MinLength(constants.FieldNames.NewPassword, req.NewPassword, constants.PasswordMinLength)
	v.MaxLength(constants.FieldNames.NewPassword, req.NewPassword, constants.PasswordMaxLength)

	if errs := v.Errors(); errs != nil {
		return errs
	}
	return nil
}

// validateUserStatus 驗證用戶狀態值
// 確保狀態值為系統允許的有效值
func (h *Handler) validateUserStatus(status sqlc.UserStatus) error {
	validStatuses := []sqlc.UserStatus{
		constants.UserStatuses.Approved,
		constants.UserStatuses.PendingChange,
		constants.UserStatuses.ChangeRejected,
		constants.UserStatuses.Deleted,
	}
	for _, valid := range validStatuses {
		if status == valid {
			return nil
		}
	}
	return errors.New(constants.ErrMsgInvalidStatus)
}

// 輔助方法

// handleServiceError 處理服務層錯誤
// 將服務層錯誤轉換為適當的 HTTP 回應
func (h *Handler) handleServiceError(w http.ResponseWriter, err error) {
	api.HandleCommonServiceError(w, err, h.logger, "user service")
}
